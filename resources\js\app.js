/**
 * Pin Board Vue.js Dashboard
 * 
 * Main application entry point
 */

// Import Vue 3 from CDN (will be loaded via script tag)
const { createApp, ref, reactive, computed, onMounted } = Vue;

// API Service
const apiService = {
    baseUrl: '/routes/api.php',
    
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}?api=${endpoint}`;
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            ...options
        };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'API request failed');
            }
            
            return data;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    },
    
    // Pin API methods
    async getTodaySuggestions() {
        return this.request('today_suggestions');
    },
    
    async getPins(page = 1, perPage = 20) {
        return this.request(`pins?page=${page}&per_page=${perPage}`);
    },
    
    async getQuotaInfo() {
        return this.request('quota');
    },
    
    async getStatistics() {
        return this.request('statistics');
    }
};

// Main Dashboard Component
const Dashboard = {
    setup() {
        // Reactive state
        const state = reactive({
            loading: false,
            error: null,
            todaySuggestions: null,
            quotaInfo: null,
            statistics: null,
            activeTab: 'suggestions'
        });
        
        // Methods
        const loadTodaySuggestions = async () => {
            try {
                state.loading = true;
                state.error = null;
                const response = await apiService.getTodaySuggestions();
                state.todaySuggestions = response;
            } catch (error) {
                state.error = error.message;
                console.error('Failed to load today suggestions:', error);
            } finally {
                state.loading = false;
            }
        };
        
        const loadQuotaInfo = async () => {
            try {
                const response = await apiService.getQuotaInfo();
                state.quotaInfo = response;
            } catch (error) {
                console.error('Failed to load quota info:', error);
            }
        };
        
        const loadStatistics = async () => {
            try {
                const response = await apiService.getStatistics();
                state.statistics = response;
            } catch (error) {
                console.error('Failed to load statistics:', error);
            }
        };
        
        const refreshData = async () => {
            await Promise.all([
                loadTodaySuggestions(),
                loadQuotaInfo(),
                loadStatistics()
            ]);
        };
        
        const setActiveTab = (tab) => {
            state.activeTab = tab;
        };
        
        // Computed properties
        const hasData = computed(() => {
            return state.todaySuggestions && !state.loading;
        });
        
        const quotaPercentage = computed(() => {
            if (!state.quotaInfo) return 0;
            const { daily_quota, submitted_today } = state.quotaInfo;
            return daily_quota > 0 ? Math.round((submitted_today / daily_quota) * 100) : 0;
        });
        
        // Lifecycle
        onMounted(() => {
            refreshData();
            // Auto-refresh every 5 minutes
            setInterval(refreshData, 5 * 60 * 1000);
        });
        
        return {
            state,
            loadTodaySuggestions,
            loadQuotaInfo,
            loadStatistics,
            refreshData,
            setActiveTab,
            hasData,
            quotaPercentage
        };
    },
    
    template: `
        <div class="pin-board-dashboard">
            <!-- Header -->
            <header class="dashboard-header">
                <h1>Pin Board Dashboard</h1>
                <div class="header-actions">
                    <button @click="refreshData" :disabled="state.loading" class="btn btn-primary">
                        <span v-if="state.loading">Refreshing...</span>
                        <span v-else>Refresh</span>
                    </button>
                </div>
            </header>
            
            <!-- Quota Info -->
            <div v-if="state.quotaInfo" class="quota-card">
                <h3>Daily Quota</h3>
                <div class="quota-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" :style="{ width: quotaPercentage + '%' }"></div>
                    </div>
                    <div class="quota-text">
                        {{ state.quotaInfo.submitted_today }} / {{ state.quotaInfo.daily_quota }} pins used
                        ({{ quotaPercentage }}%)
                    </div>
                </div>
                <div class="quota-remaining">
                    {{ state.quotaInfo.remaining_quota }} pins remaining today
                </div>
            </div>
            
            <!-- Error Message -->
            <div v-if="state.error" class="error-message">
                <strong>Error:</strong> {{ state.error }}
            </div>
            
            <!-- Loading State -->
            <div v-if="state.loading && !hasData" class="loading-state">
                <div class="spinner"></div>
                <p>Loading dashboard data...</p>
            </div>
            
            <!-- Navigation Tabs -->
            <nav v-if="hasData" class="dashboard-tabs">
                <button 
                    @click="setActiveTab('suggestions')" 
                    :class="{ active: state.activeTab === 'suggestions' }"
                    class="tab-button"
                >
                    Today's Suggestions
                </button>
                <button 
                    @click="setActiveTab('statistics')" 
                    :class="{ active: state.activeTab === 'statistics' }"
                    class="tab-button"
                >
                    Statistics
                </button>
            </nav>
            
            <!-- Tab Content -->
            <div v-if="hasData" class="tab-content">
                <!-- Today's Suggestions Tab -->
                <div v-if="state.activeTab === 'suggestions'" class="suggestions-tab">
                    <suggestions-panel :data="state.todaySuggestions" />
                </div>
                
                <!-- Statistics Tab -->
                <div v-if="state.activeTab === 'statistics'" class="statistics-tab">
                    <statistics-panel :data="state.statistics" />
                </div>
            </div>
        </div>
    `
};

// Initialize the Vue app
document.addEventListener('DOMContentLoaded', () => {
    const app = createApp(Dashboard);
    
    // Register global components (will be defined in separate files)
    app.component('suggestions-panel', SuggestionsPanel);
    app.component('statistics-panel', StatisticsPanel);
    app.component('pin-card', PinCard);
    app.component('quota-chart', QuotaChart);
    
    app.mount('#pin-board-app');
});

// Export for use in other modules
window.PinBoardApp = {
    Dashboard,
    apiService
};
