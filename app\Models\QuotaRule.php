<?php

namespace App\Models;

/**
 * QuotaRule Model
 * 
 * Manages configurable quota rules for advanced quota management
 */
class QuotaRule extends BaseModel
{
    protected $table = 'wp_quota_rules';
    
    protected $fillable = [
        'rule_name',
        'rule_type',
        'condition_field',
        'condition_operator',
        'condition_value',
        'quota_adjustment',
        'priority',
        'is_active',
        'description'
    ];

    protected $casts = [
        'condition_value' => 'json',
        'quota_adjustment' => 'integer',
        'priority' => 'integer',
        'is_active' => 'boolean'
    ];

    /**
     * Rule types
     */
    const RULE_TYPE_AGE_BASED = 'age_based';
    const RULE_TYPE_PERFORMANCE_BASED = 'performance_based';
    const RULE_TYPE_SEASONAL = 'seasonal';
    const RULE_TYPE_CUSTOM = 'custom';

    /**
     * Condition operators
     */
    const OPERATOR_GREATER_THAN = '>';
    const OPERATOR_LESS_THAN = '<';
    const OPERATOR_EQUAL = '=';
    const OPERATOR_GREATER_EQUAL = '>=';
    const OPERATOR_LESS_EQUAL = '<=';
    const OPERATOR_BETWEEN = 'between';
    const OPERATOR_IN = 'in';

    /**
     * Get active quota rules ordered by priority
     */
    public static function getActiveRules()
    {
        return static::where('is_active', true)
                    ->orderBy('priority', 'asc')
                    ->get();
    }

    /**
     * Get rules by type
     */
    public static function getRulesByType($type)
    {
        return static::where('rule_type', $type)
                    ->where('is_active', true)
                    ->orderBy('priority', 'asc')
                    ->get();
    }

    /**
     * Evaluate if this rule applies to given context
     */
    public function evaluateRule($context)
    {
        if (!$this->is_active) {
            return false;
        }

        $field_value = $this->getFieldValue($context, $this->condition_field);
        $condition_value = $this->condition_value;

        switch ($this->condition_operator) {
            case self::OPERATOR_GREATER_THAN:
                return $field_value > $condition_value;
            
            case self::OPERATOR_LESS_THAN:
                return $field_value < $condition_value;
            
            case self::OPERATOR_EQUAL:
                return $field_value == $condition_value;
            
            case self::OPERATOR_GREATER_EQUAL:
                return $field_value >= $condition_value;
            
            case self::OPERATOR_LESS_EQUAL:
                return $field_value <= $condition_value;
            
            case self::OPERATOR_BETWEEN:
                return is_array($condition_value) && 
                       $field_value >= $condition_value[0] && 
                       $field_value <= $condition_value[1];
            
            case self::OPERATOR_IN:
                return is_array($condition_value) && 
                       in_array($field_value, $condition_value);
            
            default:
                return false;
        }
    }

    /**
     * Get field value from context
     */
    private function getFieldValue($context, $field)
    {
        switch ($field) {
            case 'account_age_days':
                return $context['account_age_days'] ?? 0;
            
            case 'pins_today':
                return $context['pins_today'] ?? 0;
            
            case 'pins_this_week':
                return $context['pins_this_week'] ?? 0;
            
            case 'pins_this_month':
                return $context['pins_this_month'] ?? 0;
            
            case 'success_rate':
                return $context['success_rate'] ?? 0;
            
            case 'day_of_week':
                return date('N'); // 1 (Monday) to 7 (Sunday)
            
            case 'month':
                return date('n'); // 1 to 12
            
            case 'hour':
                return date('G'); // 0 to 23
            
            default:
                return $context[$field] ?? null;
        }
    }

    /**
     * Create default quota rules
     */
    public static function createDefaultRules()
    {
        $default_rules = [
            [
                'rule_name' => 'Basic Age-Based Quota',
                'rule_type' => self::RULE_TYPE_AGE_BASED,
                'condition_field' => 'account_age_days',
                'condition_operator' => self::OPERATOR_BETWEEN,
                'condition_value' => [0, 29],
                'quota_adjustment' => 10,
                'priority' => 1,
                'is_active' => true,
                'description' => 'New accounts (0-29 days) get 10 pins per day'
            ],
            [
                'rule_name' => 'Intermediate Age-Based Quota',
                'rule_type' => self::RULE_TYPE_AGE_BASED,
                'condition_field' => 'account_age_days',
                'condition_operator' => self::OPERATOR_BETWEEN,
                'condition_value' => [30, 89],
                'quota_adjustment' => 20,
                'priority' => 2,
                'is_active' => true,
                'description' => 'Intermediate accounts (30-89 days) get 20 pins per day'
            ],
            [
                'rule_name' => 'Advanced Age-Based Quota',
                'rule_type' => self::RULE_TYPE_AGE_BASED,
                'condition_field' => 'account_age_days',
                'condition_operator' => self::OPERATOR_BETWEEN,
                'condition_value' => [90, 149],
                'quota_adjustment' => 35,
                'priority' => 3,
                'is_active' => true,
                'description' => 'Advanced accounts (90-149 days) get 35 pins per day'
            ],
            [
                'rule_name' => 'Expert Age-Based Quota',
                'rule_type' => self::RULE_TYPE_AGE_BASED,
                'condition_field' => 'account_age_days',
                'condition_operator' => self::OPERATOR_GREATER_EQUAL,
                'condition_value' => 150,
                'quota_adjustment' => 50,
                'priority' => 4,
                'is_active' => true,
                'description' => 'Expert accounts (150+ days) get 50 pins per day'
            ],
            [
                'rule_name' => 'Weekend Bonus',
                'rule_type' => self::RULE_TYPE_SEASONAL,
                'condition_field' => 'day_of_week',
                'condition_operator' => self::OPERATOR_IN,
                'condition_value' => [6, 7], // Saturday, Sunday
                'quota_adjustment' => 5,
                'priority' => 10,
                'is_active' => true,
                'description' => 'Extra 5 pins on weekends'
            ],
            [
                'rule_name' => 'High Performance Bonus',
                'rule_type' => self::RULE_TYPE_PERFORMANCE_BASED,
                'condition_field' => 'success_rate',
                'condition_operator' => self::OPERATOR_GREATER_EQUAL,
                'condition_value' => 90,
                'quota_adjustment' => 10,
                'priority' => 15,
                'is_active' => true,
                'description' => 'Extra 10 pins for accounts with 90%+ success rate'
            ]
        ];

        foreach ($default_rules as $rule_data) {
            $existing = static::where('rule_name', $rule_data['rule_name'])->first();
            if (!$existing) {
                static::create($rule_data);
            }
        }
    }

    /**
     * Calculate quota based on rules
     */
    public static function calculateQuotaFromRules($context)
    {
        $base_quota = 10; // Default base quota
        $bonus_quota = 0;
        $applied_rules = [];

        $rules = static::getActiveRules();

        foreach ($rules as $rule) {
            if ($rule->evaluateRule($context)) {
                if ($rule->rule_type === self::RULE_TYPE_AGE_BASED) {
                    // Age-based rules set the base quota
                    $base_quota = $rule->quota_adjustment;
                } else {
                    // Other rules add bonus quota
                    $bonus_quota += $rule->quota_adjustment;
                }
                
                $applied_rules[] = [
                    'rule_name' => $rule->rule_name,
                    'adjustment' => $rule->quota_adjustment,
                    'type' => $rule->rule_type
                ];
            }
        }

        return [
            'base_quota' => $base_quota,
            'bonus_quota' => $bonus_quota,
            'total_quota' => $base_quota + $bonus_quota,
            'applied_rules' => $applied_rules
        ];
    }

    /**
     * Validate rule data
     */
    public static function validateRuleData($data)
    {
        $errors = [];

        if (empty($data['rule_name'])) {
            $errors[] = 'Rule name is required';
        }

        if (empty($data['rule_type'])) {
            $errors[] = 'Rule type is required';
        }

        if (empty($data['condition_field'])) {
            $errors[] = 'Condition field is required';
        }

        if (empty($data['condition_operator'])) {
            $errors[] = 'Condition operator is required';
        }

        if (!isset($data['condition_value'])) {
            $errors[] = 'Condition value is required';
        }

        if (!isset($data['quota_adjustment'])) {
            $errors[] = 'Quota adjustment is required';
        }

        return $errors;
    }
}
