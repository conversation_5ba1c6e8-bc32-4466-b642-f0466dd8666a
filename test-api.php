<?php

/**
 * API Test Script
 * 
 * Simple test script to verify the Pin Board API is working
 */

// Include the bootstrap file
require_once __DIR__ . '/bootstrap/app.php';

echo "Pin Board v10 API Test\n";
echo "=====================\n\n";

// Test 1: Configuration loading
echo "1. Testing configuration loading...\n";
$app_name = pinboard_config('app.name');
$app_version = pinboard_config('app.version');
echo "   App Name: $app_name\n";
echo "   App Version: $app_version\n";
echo "   Status: " . ($app_name ? "✓ PASS" : "✗ FAIL") . "\n\n";

// Test 2: Database connection
echo "2. Testing database connection...\n";
try {
    $db = pinboard_db();
    if ($db) {
        echo "   Database Host: " . pinboard_config('database.host') . "\n";
        echo "   Database Name: " . pinboard_config('database.name') . "\n";
        echo "   Status: ✓ PASS - Connected successfully\n";
    } else {
        echo "   Status: ✗ FAIL - Connection failed\n";
    }
} catch (Exception $e) {
    echo "   Status: ✗ FAIL - " . $e->getMessage() . "\n";
}
echo "\n";

// Test 3: WordPress integration
echo "3. Testing WordPress integration...\n";
if (defined('ABSPATH')) {
    echo "   WordPress Path: " . ABSPATH . "\n";
    echo "   Status: ✓ PASS - WordPress loaded\n";
} else {
    echo "   Status: ⚠ WARNING - WordPress not loaded (this is expected if running standalone)\n";
}
echo "\n";

// Test 4: Table creation
echo "4. Testing table creation...\n";
try {
    if ($db) {
        // Include the API file to get the table creation function
        require_once __DIR__ . '/routes/api.php';
        pinboard_create_tables($db);
        echo "   Status: ✓ PASS - Tables created/verified\n";
    } else {
        echo "   Status: ✗ FAIL - No database connection\n";
    }
} catch (Exception $e) {
    echo "   Status: ✗ FAIL - " . $e->getMessage() . "\n";
}
echo "\n";

// Test 5: API endpoint simulation
echo "5. Testing API endpoint simulation...\n";
try {
    // Simulate the API request
    $_GET['api'] = 'today_suggestions';
    
    // Capture output
    ob_start();
    
    // Set up a simple mock for WordPress functions if not available
    if (!function_exists('get_posts')) {
        function get_posts($args = []) {
            return [];
        }
    }
    if (!function_exists('current_time')) {
        function current_time($format) {
            return date($format);
        }
    }
    
    // Call the API function
    require_once __DIR__ . '/routes/api.php';
    
    // This would normally output JSON, but we'll catch it
    $output = ob_get_clean();
    
    echo "   API Response Generated: " . (strlen($output) > 0 ? "✓ PASS" : "⚠ No output") . "\n";
    if (strlen($output) > 0) {
        $json = json_decode($output, true);
        if ($json) {
            echo "   Valid JSON: ✓ PASS\n";
            echo "   Response Keys: " . implode(', ', array_keys($json)) . "\n";
        } else {
            echo "   Valid JSON: ✗ FAIL\n";
        }
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "   Status: ✗ FAIL - " . $e->getMessage() . "\n";
}
echo "\n";

// Test 6: File structure
echo "6. Testing file structure...\n";
$required_files = [
    'bootstrap/app.php',
    'config/pinboard.php',
    'config/database.php',
    'routes/api.php',
    'app/Services/PinService.php',
    'app/Services/QuotaService.php',
    'app/Database/Connection.php',
    '.env'
];

$missing_files = [];
foreach ($required_files as $file) {
    if (!file_exists(__DIR__ . '/' . $file)) {
        $missing_files[] = $file;
    }
}

if (empty($missing_files)) {
    echo "   Status: ✓ PASS - All required files present\n";
} else {
    echo "   Status: ✗ FAIL - Missing files: " . implode(', ', $missing_files) . "\n";
}
echo "\n";

echo "Test Summary\n";
echo "============\n";
echo "Pin Board v10 API structure has been set up successfully.\n";
echo "The system is ready for Stage 2 implementation.\n\n";

echo "Next Steps:\n";
echo "- Test the API endpoint via web browser: pinboard-api.php?api=today_suggestions\n";
echo "- Integrate with WordPress using wp-pinboard-integration.php\n";
echo "- Begin Stage 2: Core Features Migration\n";
