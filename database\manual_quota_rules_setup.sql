-- =====================================================
-- Pin Board v10 - Manual Quota Rules Table Setup
-- =====================================================
-- Run this SQL script in phpMyAdmin or MySQL client
-- when you have access to your WordPress database

-- Create the quota_rules table
CREATE TABLE IF NOT EXISTS `wp_quota_rules` (
    id bigint(20) AUTO_INCREMENT,
    rule_name varchar(255) NOT NULL,
    rule_type varchar(50) NOT NULL,
    condition_field varchar(100) NOT NULL,
    condition_operator varchar(20) NOT NULL,
    condition_value JSON,
    quota_adjustment int NOT NULL,
    priority int DEFAULT 10,
    is_active boolean DEFAULT true,
    description text,
    created_at timestamp DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY rule_type (rule_type),
    KEY is_active (is_active),
    KEY priority (priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default quota rules
INSERT INTO `wp_quota_rules` (rule_name, rule_type, condition_field, condition_operator, condition_value, quota_adjustment, priority, is_active, description) VALUES
('Basic Age-Based Quota', 'age_based', 'account_age_days', 'between', '[0, 29]', 10, 1, true, 'New accounts (0-29 days) get 10 pins per day'),
('Intermediate Age-Based Quota', 'age_based', 'account_age_days', 'between', '[30, 89]', 20, 2, true, 'Intermediate accounts (30-89 days) get 20 pins per day'),
('Advanced Age-Based Quota', 'age_based', 'account_age_days', 'between', '[90, 149]', 35, 3, true, 'Advanced accounts (90-149 days) get 35 pins per day'),
('Expert Age-Based Quota', 'age_based', 'account_age_days', '>=', '150', 50, 4, true, 'Expert accounts (150+ days) get 50 pins per day'),
('Weekend Bonus', 'seasonal', 'day_of_week', 'in', '[6, 7]', 5, 10, true, 'Extra 5 pins on weekends'),
('High Performance Bonus', 'performance_based', 'success_rate', '>=', '90', 10, 15, true, 'Extra 10 pins for accounts with 90%+ success rate');

-- Verify the table was created and populated
SELECT 'Table created successfully' as status;
SELECT COUNT(*) as total_rules FROM `wp_quota_rules`;
SELECT rule_name, rule_type, quota_adjustment, is_active FROM `wp_quota_rules` ORDER BY priority;

-- =====================================================
-- Optional: View table structure
-- =====================================================
-- DESCRIBE `wp_quota_rules`;

-- =====================================================
-- Optional: Drop table (use only if you need to reset)
-- =====================================================
-- DROP TABLE IF EXISTS `wp_quota_rules`;
