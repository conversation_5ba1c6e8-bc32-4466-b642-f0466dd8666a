<?php

use App\Models\PinHistory;
use App\Models\PinDetail;
use App\Models\PendingPin;
use App\Models\IgnoredPost;

/**
 * API Routes for Pin Board v10
 *
 * These routes maintain compatibility with the existing Chrome extension
 * while providing a modern structure for future development using ORM models
 */

// Include the bootstrap file
require_once dirname(__DIR__) . '/bootstrap/app.php';

/**
 * Route handler for API requests
 */
function pinboard_handle_api_request() {
    $api_action = pinboard_request('api');
    
    switch ($api_action) {
        case 'today_suggestions':
            return pinboard_api_today_suggestions();
            
        default:
            return pinboard_response([
                'success' => false,
                'error' => 'Invalid API endpoint'
            ], 404);
    }
}

/**
 * Today's pin suggestions API endpoint
 * Maintains compatibility with existing Chrome extension
 */
function pinboard_api_today_suggestions() {
    try {
        // Set timezone
        date_default_timezone_set(pinboard_config('timezone', 'Asia/Dhaka'));
        
        // Get database connection
        $db = pinboard_db();
        if (!$db) {
            throw new Exception('Database connection failed');
        }
        
        // Create tables if they don't exist
        pinboard_create_tables($db);
        
        $today_str = current_time('Y-m-d');
        $today = new DateTime($today_str, new DateTimeZone(pinboard_config('timezone', 'Asia/Dhaka')));
        
        // Get WordPress posts
        $posts = pinboard_get_posts();
        
        // Load existing data using ORM models
        $pin_history = pinboard_load_pin_history();
        $ignored_posts = pinboard_load_ignored_posts();

        // Get candidates
        $new_pin_candidates = pinboard_get_new_pin_candidates($posts, $pin_history, $today_str, $ignored_posts);
        $prioritized_candidates = pinboard_prioritize_new_pin_candidates($new_pin_candidates);
        $repin_candidates = pinboard_get_repin_candidates($posts, $pin_history, $today_str, $ignored_posts);

        // Calculate quota using ORM models
        $quota_info = pinboard_calculate_quota(null, $today);
        $daily_quota = $quota_info['remaining_quota'];
        
        // Apply quota limits
        $max_new_pins = min(ceil($daily_quota * pinboard_config('quota.new_pin_percentage', 0.4)), count($prioritized_candidates));
        $max_repins = min($daily_quota - $max_new_pins, count($repin_candidates));
        
        // Format response
        $response = [
            'success' => true,
            'date' => $today_str,
            'quota_info' => $quota_info,
            'new_pins' => [],
            're_pins' => []
        ];
        
        // Add new pins
        for ($i = 0; $i < $max_new_pins; $i++) {
            if (isset($prioritized_candidates[$i])) {
                $response['new_pins'][] = [
                    'title' => $prioritized_candidates[$i]['title'],
                    'url' => $prioritized_candidates[$i]['url']
                ];
            }
        }
        
        // Add re-pins
        for ($i = 0; $i < $max_repins; $i++) {
            if (isset($repin_candidates[$i])) {
                $response['re_pins'][] = [
                    'title' => $repin_candidates[$i]['title'],
                    'url' => $repin_candidates[$i]['url']
                ];
            }
        }
        
        return pinboard_response($response);
        
    } catch (Exception $e) {
        error_log('Pin Board API Error: ' . $e->getMessage());
        return pinboard_response([
            'success' => false,
            'error' => 'Internal server error'
        ], 500);
    }
}

/**
 * Create database tables if they don't exist
 */
function pinboard_create_tables($db) {
    $tables = pinboard_config('tables');
    
    $sql_statements = [
        $tables['pin_history'] => "CREATE TABLE IF NOT EXISTS `{$tables['pin_history']}` (
            id bigint(20) AUTO_INCREMENT,
            post_id bigint(20) NOT NULL UNIQUE,
            title varchar(255),
            url varchar(255),
            publish_date date,
            PRIMARY KEY (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        $tables['pin_details'] => "CREATE TABLE IF NOT EXISTS `{$tables['pin_details']}` (
            id bigint(20) AUTO_INCREMENT,
            history_id bigint(20) NOT NULL,
            pin_link varchar(255),
            pin_date date,
            PRIMARY KEY (id),
            KEY history_id (history_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        $tables['pending_pins'] => "CREATE TABLE IF NOT EXISTS `{$tables['pending_pins']}` (
            id bigint(20) AUTO_INCREMENT,
            post_id bigint(20) NOT NULL,
            event_type varchar(10),
            scheduled_date date,
            retry_count int DEFAULT 0,
            PRIMARY KEY (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        
        $tables['ignored_posts'] => "CREATE TABLE IF NOT EXISTS `{$tables['ignored_posts']}` (
            post_id bigint(20) NOT NULL UNIQUE,
            ignored_date date,
            PRIMARY KEY (post_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;"
    ];
    
    foreach ($sql_statements as $table_name => $sql) {
        if (!$db->query($sql)) {
            throw new Exception("Error creating table $table_name: " . $db->error);
        }
    }
}

// Handle API request if this file is accessed directly
if (isset($_GET['api']) && $_GET['api'] === 'today_suggestions') {
    pinboard_handle_api_request();
}
