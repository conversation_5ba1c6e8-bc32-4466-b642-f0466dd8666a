<?php

/**
 * Pin Service
 * 
 * Core business logic for pin management
 */

/**
 * Get WordPress posts for pin processing
 */
function pinboard_get_posts() {
    if (!function_exists('get_posts')) {
        return [];
    }
    
    $args = [
        'post_status' => pinboard_config('wordpress.post_status', ['publish']),
        'posts_per_page' => -1,
        'orderby' => 'date',
        'order' => 'DESC',
        'cache_results' => true,
        'post_type' => pinboard_config('wordpress.post_types', ['post'])
    ];
    
    return get_posts($args);
}

/**
 * Load pin history from database
 */
function pinboard_load_pin_history($db) {
    $tables = pinboard_config('tables');
    $pin_history = [];
    
    // Reset expired pins first
    pinboard_reset_expired_pins($db);
    
    $query = "SELECT h.*, d.pin_link, d.pin_date
              FROM `{$tables['pin_history']}` h
              LEFT JOIN `{$tables['pin_details']}` d ON h.id = d.history_id
              ORDER BY h.post_id";
              
    $result = $db->query($query);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $post_id = $row['post_id'];
            if (!isset($pin_history[$post_id])) {
                $pin_history[$post_id] = [
                    'title' => $row['title'],
                    'url' => $row['url'],
                    'publish_date' => $row['publish_date'],
                    'pins' => []
                ];
            }
            if ($row['pin_link']) {
                $pin_history[$post_id]['pins'][] = [
                    'link' => $row['pin_link'],
                    'date' => $row['pin_date']
                ];
            }
        }
        $result->free();
    }
    
    return $pin_history;
}

/**
 * Load ignored posts from database
 */
function pinboard_load_ignored_posts($db) {
    $tables = pinboard_config('tables');
    $ignored = [];
    
    $query = "SELECT post_id FROM `{$tables['ignored_posts']}`";
    $result = $db->query($query);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $ignored[] = $row['post_id'];
        }
        $result->free();
    }
    
    return $ignored;
}

/**
 * Reset pins that are older than the threshold
 */
function pinboard_reset_expired_pins($db) {
    $tables = pinboard_config('tables');
    $threshold_days = pinboard_config('reset.days_threshold', 151);
    $reset_date = date('Y-m-d', strtotime("-{$threshold_days} days"));
    
    // Find posts with first pin older than threshold
    $query = "SELECT h.id as history_id, h.post_id, MIN(d.pin_date) as first_pin_date
              FROM `{$tables['pin_history']}` h
              INNER JOIN `{$tables['pin_details']}` d ON h.id = d.history_id
              GROUP BY h.id, h.post_id
              HAVING first_pin_date <= '$reset_date'";
    
    $result = $db->query($query);
    if ($result) {
        $db->begin_transaction();
        try {
            while ($row = $result->fetch_assoc()) {
                $history_id = $row['history_id'];
                // Delete all pin details for this post
                $delete_stmt = $db->prepare("DELETE FROM `{$tables['pin_details']}` WHERE history_id = ?");
                $delete_stmt->bind_param("i", $history_id);
                $delete_stmt->execute();
                $delete_stmt->close();
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            error_log("Error resetting expired pins: " . $e->getMessage());
        }
        $result->free();
    }
}

/**
 * Get new pin candidates (listicle posts that haven't been pinned)
 */
function pinboard_get_new_pin_candidates($posts, $pin_history, $today_str, $ignored_posts) {
    $candidates = [];
    $patterns = pinboard_config('filtering.listicle_patterns', ['/\d+/', '/list/i']);
    
    foreach ($posts as $post) {
        $post_id = $post->ID;
        
        // Skip ignored posts
        if (in_array($post_id, $ignored_posts)) {
            continue;
        }
        
        // Check if it's a listicle
        $title = get_the_title($post_id);
        $is_listicle = false;
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $title)) {
                $is_listicle = true;
                break;
            }
        }
        
        if (!$is_listicle) {
            continue;
        }
        
        // Skip if already has pins
        if (isset($pin_history[$post_id]) && !empty($pin_history[$post_id]['pins'])) {
            continue;
        }
        
        $url = get_permalink($post_id);
        $publish_date = get_the_date('Y-m-d', $post_id);
        $publish_time = strtotime($publish_date);
        $today_time = strtotime($today_str);
        $days_old = floor(($today_time - $publish_time) / (60 * 60 * 24));
        
        $candidates[] = [
            'id' => $post_id,
            'title' => $title,
            'url' => $url,
            'publish_date' => $publish_date,
            'days_old' => $days_old,
            'event_type' => 'new_pin',
            'pins' => [],
            'total_pins' => 5,
            'current_pin' => 1
        ];
    }
    
    return $candidates;
}

/**
 * Prioritize new pin candidates
 */
function pinboard_prioritize_new_pin_candidates($candidates) {
    usort($candidates, function($a, $b) {
        // Today's posts first
        if ($a['days_old'] === 0 && $b['days_old'] !== 0) return -1;
        if ($b['days_old'] === 0 && $a['days_old'] !== 0) return 1;
        
        // Then posts within 7 days
        if ($a['days_old'] <= 7 && $b['days_old'] > 7) return -1;
        if ($b['days_old'] <= 7 && $a['days_old'] > 7) return 1;
        
        // Then posts within 30 days
        if ($a['days_old'] <= 30 && $b['days_old'] > 30) return -1;
        if ($b['days_old'] <= 30 && $a['days_old'] > 30) return 1;
        
        // Finally by age (older first)
        return $a['days_old'] - $b['days_old'];
    });
    
    return $candidates;
}

/**
 * Get repin candidates (posts with incomplete pin sets)
 */
function pinboard_get_repin_candidates($posts, $pin_history, $today_str, $ignored_posts) {
    $candidates = [];
    $patterns = pinboard_config('filtering.listicle_patterns', ['/\d+/', '/list/i']);
    
    foreach ($posts as $post) {
        $post_id = $post->ID;
        
        // Skip ignored posts
        if (in_array($post_id, $ignored_posts)) {
            continue;
        }
        
        // Check if it's a listicle
        $title = get_the_title($post_id);
        $is_listicle = false;
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $title)) {
                $is_listicle = true;
                break;
            }
        }
        
        if (!$is_listicle) {
            continue;
        }
        
        $url = get_permalink($post_id);
        
        if (isset($pin_history[$post_id])) {
            $post_data = $pin_history[$post_id];
            $pins = $post_data['pins'];
            $pin_count = count($pins);
            
            // Only consider posts with 1-4 pins
            if ($pin_count > 0 && $pin_count < 5) {
                $last_pin = end($pins);
                $last_pin_date = strtotime($last_pin['date']);
                $days_since_last_pin = (strtotime($today_str) - $last_pin_date) / (60 * 60 * 24);
                
                // Only repin if it's been at least 7 days
                if ($days_since_last_pin >= 7) {
                    $candidates[] = [
                        'id' => $post_id,
                        'title' => $title,
                        'url' => $url,
                        'event_type' => 'repin',
                        'scheduled_date' => $last_pin['date'],
                        'pin_count' => $pin_count,
                        'days_since_last' => floor($days_since_last_pin),
                        'retry_count' => 0,
                        'pins' => $pins,
                        'total_pins' => 5,
                        'current_pin' => $pin_count + 1
                    ];
                }
            }
        }
    }
    
    // Sort by days since last pin (descending) and then by pin count (ascending)
    usort($candidates, function($a, $b) {
        $days_diff = $b['days_since_last'] - $a['days_since_last'];
        if ($days_diff !== 0) return $days_diff;
        return $a['pin_count'] - $b['pin_count'];
    });
    
    return $candidates;
}
