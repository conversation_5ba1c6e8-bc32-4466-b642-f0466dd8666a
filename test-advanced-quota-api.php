<?php

/**
 * Test script for Advanced Quota Management API endpoints
 */

// Include the bootstrap file
require_once __DIR__ . '/bootstrap/app.php';

use App\Models\QuotaRule;

echo "<h2>Pin Board v10 - Advanced Quota API Test</h2>\n";

// Test endpoints
$test_endpoints = [
    'quota' => 'Basic quota information',
    'quota_advanced' => 'Advanced quota with rule breakdown',
    'quota_stats' => 'Quota statistics',
    'quota_suggestions' => 'Quota improvement suggestions',
    'quota_optimization_tips' => 'Optimization tips',
    'quota_efficiency' => 'Efficiency analysis',
    'quota_rules' => 'All quota rules',
    'quota_rules_active' => 'Active quota rules only',
    'quota_rules_metadata' => 'Rule metadata for forms'
];

foreach ($test_endpoints as $endpoint => $description) {
    echo "<h3>Testing: {$endpoint}</h3>\n";
    echo "<p><em>{$description}</em></p>\n";
    
    try {
        // Simulate API request
        $_GET['api'] = $endpoint;
        
        ob_start();
        $result = pinboard_handle_api_request();
        $output = ob_get_clean();
        
        if ($result) {
            echo "<div style='background: #f0f8f0; padding: 10px; border: 1px solid #ccc; margin: 10px 0;'>\n";
            echo "<strong>Response:</strong><br>\n";
            echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT)) . "</pre>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #fff0f0; padding: 10px; border: 1px solid #fcc; margin: 10px 0;'>\n";
            echo "<strong>No response or error</strong>\n";
            if ($output) {
                echo "<br>Output: " . htmlspecialchars($output);
            }
            echo "</div>\n";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #fff0f0; padding: 10px; border: 1px solid #fcc; margin: 10px 0;'>\n";
        echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "\n";
        echo "</div>\n";
    }
    
    // Clear the GET parameter
    unset($_GET['api']);
    
    echo "<hr>\n";
}

// Test quota rule creation
echo "<h3>Testing: Quota Rule Creation</h3>\n";
echo "<p><em>Create a custom quota rule</em></p>\n";

try {
    
    $test_rule_data = [
        'rule_name' => 'Test Custom Rule',
        'rule_type' => QuotaRule::RULE_TYPE_CUSTOM,
        'condition_field' => 'account_age_days',
        'condition_operator' => QuotaRule::OPERATOR_GREATER_THAN,
        'condition_value' => 30,
        'quota_adjustment' => 5,
        'priority' => 20,
        'is_active' => true,
        'description' => 'Test rule created by API test script'
    ];
    
    // Check if rule already exists
    $existing = QuotaRule::where('rule_name', $test_rule_data['rule_name'])->first();
    
    if (!$existing) {
        $rule = QuotaRule::create($test_rule_data);
        echo "<div style='background: #f0f8f0; padding: 10px; border: 1px solid #ccc; margin: 10px 0;'>\n";
        echo "<strong>✅ Test rule created successfully!</strong><br>\n";
        echo "<pre>" . htmlspecialchars(json_encode($rule->toArray(), JSON_PRETTY_PRINT)) . "</pre>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #f8f8f0; padding: 10px; border: 1px solid #ccc; margin: 10px 0;'>\n";
        echo "<strong>ℹ️ Test rule already exists</strong><br>\n";
        echo "<pre>" . htmlspecialchars(json_encode($existing->toArray(), JSON_PRETTY_PRINT)) . "</pre>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #fff0f0; padding: 10px; border: 1px solid #fcc; margin: 10px 0;'>\n";
    echo "<strong>Error creating test rule:</strong> " . htmlspecialchars($e->getMessage()) . "\n";
    echo "</div>\n";
}

echo "<hr>\n";

// Test quota calculation comparison
echo "<h3>Quota Calculation Comparison</h3>\n";
echo "<p><em>Compare legacy vs advanced quota calculation</em></p>\n";

try {
    $db = pinboard_db();
    $today = new DateTime('now', new DateTimeZone(pinboard_config('timezone', 'Asia/Dhaka')));
    
    // Legacy calculation
    $legacy_quota = pinboard_calculate_quota($db, $today);
    
    // Advanced calculation
    $advanced_quota = pinboard_calculate_advanced_quota($db, $today);
    
    echo "<div style='display: flex; gap: 20px;'>\n";
    
    echo "<div style='flex: 1; background: #f0f8f0; padding: 10px; border: 1px solid #ccc;'>\n";
    echo "<h4>Legacy Quota System</h4>\n";
    echo "<pre>" . htmlspecialchars(json_encode($legacy_quota, JSON_PRETTY_PRINT)) . "</pre>\n";
    echo "</div>\n";
    
    echo "<div style='flex: 1; background: #f0f0f8; padding: 10px; border: 1px solid #ccc;'>\n";
    echo "<h4>Advanced Quota System</h4>\n";
    echo "<pre>" . htmlspecialchars(json_encode($advanced_quota, JSON_PRETTY_PRINT)) . "</pre>\n";
    echo "</div>\n";
    
    echo "</div>\n";
    
    // Comparison summary
    echo "<div style='background: #f8f8f0; padding: 10px; border: 1px solid #ccc; margin: 10px 0;'>\n";
    echo "<h4>Comparison Summary</h4>\n";
    echo "<ul>\n";
    echo "<li><strong>Legacy Daily Quota:</strong> " . $legacy_quota['daily_quota'] . "</li>\n";
    echo "<li><strong>Advanced Daily Quota:</strong> " . $advanced_quota['daily_quota'] . "</li>\n";
    echo "<li><strong>Difference:</strong> " . ($advanced_quota['daily_quota'] - $legacy_quota['daily_quota']) . "</li>\n";
    if (isset($advanced_quota['applied_rules'])) {
        echo "<li><strong>Applied Rules:</strong> " . count($advanced_quota['applied_rules']) . "</li>\n";
    }
    if (isset($advanced_quota['bonus_quota'])) {
        echo "<li><strong>Bonus Quota:</strong> " . $advanced_quota['bonus_quota'] . "</li>\n";
    }
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #fff0f0; padding: 10px; border: 1px solid #fcc; margin: 10px 0;'>\n";
    echo "<strong>Error in quota comparison:</strong> " . htmlspecialchars($e->getMessage()) . "\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<p><a href='dashboard.php'>← Back to Dashboard</a> | <a href='test-quota-migration.php'>Migration Test</a></p>\n";
?>
