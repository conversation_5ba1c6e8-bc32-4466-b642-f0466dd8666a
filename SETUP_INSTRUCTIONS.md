# 🚀 Pin Board v10 - Setup Instructions

## Current Status
✅ **Stage 1**: Foundation & Setup (Complete)  
✅ **Stage 2**: Core Features Migration (Complete)  
🔄 **Stage 3**: Advanced Features Implementation (In Progress)  
- ✅ Task 1.1: Advanced Quota Management System (Complete)
- ⏳ Task 1.2: Pin Reset System for Expired Pins (Next)

## 📋 Prerequisites

Since you don't have PHP server installed, you'll need to set up a local development environment:

### Option 1: XAMPP (Recommended)
1. Download XAMPP from https://www.apachefriends.org/
2. Install XAMPP with Apache, MySQL, and PHP
3. Start Apache and MySQL services
4. Place your Pin Board v10 folder in `xampp/htdocs/`

### Option 2: WAMP (Windows)
1. Download WAMP from https://www.wampserver.com/
2. Install and start all services
3. Place your project in `wamp64/www/`

### Option 3: MAMP (Mac)
1. Download MAMP from https://www.mamp.info/
2. Install and start services
3. Place your project in the htdocs folder

## 🗄️ Database Setup

### Step 1: Create the Quota Rules Table
1. Open phpMyAdmin (usually at http://localhost/phpmyadmin)
2. Select your WordPress database
3. Go to SQL tab
4. Copy and paste the contents of `database/manual_quota_rules_setup.sql`
5. Click "Go" to execute the script

### Step 2: Verify Table Creation
After running the SQL script, you should see:
- A new table `wp_quota_rules` with 6 default rules
- Confirmation messages showing the table was created successfully

## 🧪 Testing the Implementation

### Step 1: Open the Test Interface
1. Start your PHP server (XAMPP/WAMP/MAMP)
2. Open `test-quota-api-interface.html` in your browser
3. This provides a user-friendly interface to test all API endpoints

### Step 2: Test Individual Endpoints
You can also test endpoints directly in your browser:

**Basic Quota Information:**
```
http://localhost/Pin_Board_v10/routes/api.php?api=quota
```

**Advanced Quota with Rules:**
```
http://localhost/Pin_Board_v10/routes/api.php?api=quota_advanced
```

**All Quota Rules:**
```
http://localhost/Pin_Board_v10/routes/api.php?api=quota_rules
```

**Active Rules Only:**
```
http://localhost/Pin_Board_v10/routes/api.php?api=quota_rules_active
```

### Step 3: Verify Chrome Extension Compatibility
The original Chrome extension endpoint should still work:
```
http://localhost/Pin_Board_v10/routes/api.php?api=today_suggestions
```

## 🔍 What to Expect

### Advanced Quota System Features:
1. **Rule-Based Calculation**: Quota is calculated using configurable rules
2. **Multiple Rule Types**: Age-based, performance-based, seasonal, custom
3. **Bonus System**: Weekend bonuses, performance bonuses
4. **Backward Compatibility**: Legacy quota system still works
5. **API Management**: Full CRUD operations for quota rules

### Sample Response (Advanced Quota):
```json
{
  "success": true,
  "data": {
    "daily_quota": 25,
    "base_quota": 20,
    "bonus_quota": 5,
    "submitted_today": 3,
    "remaining_quota": 22,
    "account_age_days": 45,
    "applied_rules": [
      {
        "rule_name": "Intermediate Age-Based Quota",
        "adjustment": 20,
        "type": "age_based"
      },
      {
        "rule_name": "Weekend Bonus",
        "adjustment": 5,
        "type": "seasonal"
      }
    ]
  }
}
```

## 🐛 Troubleshooting

### Common Issues:

**1. "Database connection failed"**
- Make sure MySQL is running in XAMPP/WAMP
- Check WordPress database credentials in wp-config.php

**2. "Table doesn't exist"**
- Run the SQL script in `database/manual_quota_rules_setup.sql`
- Verify the table was created in phpMyAdmin

**3. "Class not found" errors**
- Make sure all files are in the correct locations
- Check that bootstrap/app.php is loading properly

**4. API returns empty response**
- Check PHP error logs in XAMPP/WAMP
- Verify the API endpoint URL is correct
- Make sure the server is running

### Debug Steps:
1. Check PHP error logs (usually in xampp/apache/logs/)
2. Enable WordPress debug mode in wp-config.php
3. Use browser developer tools to check network requests
4. Test with simple endpoints first (like `quota_rules_metadata`)

## 📁 File Structure Verification

Make sure these files exist and are in the correct locations:

```
Pin_Board_v10/
├── app/
│   ├── Models/QuotaRule.php ✅
│   ├── Controllers/QuotaRuleController.php ✅
│   └── Services/QuotaService.php ✅ (updated)
├── config/pinboard.php ✅ (updated)
├── routes/api.php ✅ (updated)
├── database/
│   └── manual_quota_rules_setup.sql ✅
├── test-quota-api-interface.html ✅
└── SETUP_INSTRUCTIONS.md ✅
```

## ✅ Success Criteria

You'll know everything is working when:
1. ✅ SQL script runs without errors
2. ✅ `wp_quota_rules` table exists with 6 default rules
3. ✅ API endpoints return JSON responses (not errors)
4. ✅ Advanced quota shows rule breakdown
5. ✅ Chrome extension endpoint still works
6. ✅ Test interface loads and functions properly

## 🚀 Next Steps

Once you have the PHP server set up and the advanced quota system tested:
1. We can proceed with **Task 1.2: Pin Reset System for Expired Pins**
2. Continue with the remaining Stage 3 tasks
3. Move on to Stage 4: Polish & Optimization

Let me know when you have the server set up and if you encounter any issues!
