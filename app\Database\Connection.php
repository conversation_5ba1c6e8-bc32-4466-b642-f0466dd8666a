<?php

namespace App\Database;

/**
 * Database Connection Class
 * 
 * Simple database connection wrapper for Pin Board v10
 */
class Connection
{
    private static $instance = null;
    private $connection = null;
    private $config = [];

    private function __construct()
    {
        $this->config = pinboard_config('database');
        $this->connect();
    }

    /**
     * Get singleton instance
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Get the database connection
     */
    public function getConnection()
    {
        if ($this->connection === null || !$this->connection->ping()) {
            $this->connect();
        }
        return $this->connection;
    }

    /**
     * Connect to database
     */
    private function connect()
    {
        try {
            $this->connection = new \mysqli(
                $this->config['host'],
                $this->config['user'],
                $this->config['password'],
                $this->config['name']
            );

            if ($this->connection->connect_error) {
                throw new \Exception('Database connection failed: ' . $this->connection->connect_error);
            }

            $this->connection->set_charset($this->config['charset']);
        } catch (\Exception $e) {
            error_log('Pin Board Database Connection Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Execute a query
     */
    public function query($sql)
    {
        $connection = $this->getConnection();
        $result = $connection->query($sql);
        
        if ($result === false) {
            error_log('Pin Board Database Query Error: ' . $connection->error . ' | SQL: ' . $sql);
            throw new \Exception('Database query failed: ' . $connection->error);
        }
        
        return $result;
    }

    /**
     * Prepare a statement
     */
    public function prepare($sql)
    {
        $connection = $this->getConnection();
        $stmt = $connection->prepare($sql);
        
        if ($stmt === false) {
            error_log('Pin Board Database Prepare Error: ' . $connection->error . ' | SQL: ' . $sql);
            throw new \Exception('Database prepare failed: ' . $connection->error);
        }
        
        return $stmt;
    }

    /**
     * Begin transaction
     */
    public function beginTransaction()
    {
        return $this->getConnection()->begin_transaction();
    }

    /**
     * Commit transaction
     */
    public function commit()
    {
        return $this->getConnection()->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback()
    {
        return $this->getConnection()->rollback();
    }

    /**
     * Get last insert ID
     */
    public function lastInsertId()
    {
        return $this->getConnection()->insert_id;
    }

    /**
     * Get affected rows
     */
    public function affectedRows()
    {
        return $this->getConnection()->affected_rows;
    }

    /**
     * Escape string
     */
    public function escape($string)
    {
        return $this->getConnection()->real_escape_string($string);
    }

    /**
     * Get table name with prefix
     */
    public function getTableName($table)
    {
        $tables = pinboard_config('tables', []);
        return $tables[$table] ?? $table;
    }

    /**
     * Close connection
     */
    public function close()
    {
        if ($this->connection) {
            $this->connection->close();
            $this->connection = null;
        }
    }

    /**
     * Prevent cloning
     */
    private function __clone() {}

    /**
     * Prevent unserialization
     */
    private function __wakeup() {}

    /**
     * Close connection on destruction
     */
    public function __destruct()
    {
        $this->close();
    }
}
