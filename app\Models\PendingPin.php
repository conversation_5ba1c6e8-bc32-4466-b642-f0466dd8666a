<?php

namespace App\Models;

/**
 * PendingPin Model
 * 
 * Represents the wp_pending_pins table
 * Stores pins that are scheduled or pending processing
 */
class PendingPin extends BaseModel
{
    protected $table = 'pending_pins';
    
    protected $fillable = [
        'post_id',
        'event_type',
        'scheduled_date',
        'retry_count'
    ];

    // Event types
    const EVENT_TYPE_PIN = 'pin';
    const EVENT_TYPE_REPIN = 'repin';
    const EVENT_TYPE_SCHEDULE = 'schedule';

    /**
     * Get pending pins for today
     */
    public static function getTodaysPending()
    {
        $today = date('Y-m-d');
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `scheduled_date` <= ? ORDER BY `scheduled_date` ASC, `id` ASC";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('s', $today);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $models = [];
        while ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }
        
        $stmt->close();
        return $models;
    }

    /**
     * Get overdue pending pins
     */
    public static function getOverduePending()
    {
        $today = date('Y-m-d');
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `scheduled_date` < ? ORDER BY `scheduled_date` ASC";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('s', $today);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $models = [];
        while ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }
        
        $stmt->close();
        return $models;
    }

    /**
     * Find by post ID
     */
    public static function findByPostId($postId)
    {
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `post_id` = ? LIMIT 1";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return null;
        }
        
        $stmt->bind_param('i', $postId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $stmt->close();
            return $model;
        }
        
        $stmt->close();
        return null;
    }

    /**
     * Schedule a pin for a specific date
     */
    public static function schedulePin($postId, $scheduledDate, $eventType = self::EVENT_TYPE_PIN)
    {
        // Check if already scheduled
        $existing = static::findByPostId($postId);
        if ($existing) {
            $existing->scheduled_date = $scheduledDate;
            $existing->event_type = $eventType;
            $existing->retry_count = 0;
            $existing->save();
            return $existing;
        }
        
        return static::create([
            'post_id' => $postId,
            'event_type' => $eventType,
            'scheduled_date' => $scheduledDate,
            'retry_count' => 0
        ]);
    }

    /**
     * Increment retry count
     */
    public function incrementRetry()
    {
        $this->retry_count = ($this->retry_count ?? 0) + 1;
        return $this->save();
    }

    /**
     * Check if max retries reached
     */
    public function hasMaxRetriesReached($maxRetries = 3)
    {
        return ($this->retry_count ?? 0) >= $maxRetries;
    }

    /**
     * Reschedule for next day
     */
    public function rescheduleForNextDay()
    {
        $nextDay = date('Y-m-d', strtotime($this->scheduled_date . ' +1 day'));
        $this->scheduled_date = $nextDay;
        $this->retry_count = 0;
        return $this->save();
    }

    /**
     * Get pending pins by event type
     */
    public static function getByEventType($eventType)
    {
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `event_type` = ? ORDER BY `scheduled_date` ASC";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('s', $eventType);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $models = [];
        while ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }
        
        $stmt->close();
        return $models;
    }

    /**
     * Get pending pins statistics
     */
    public static function getStatistics()
    {
        $instance = new static();
        $table = $instance->getTable();
        
        // Total pending
        $totalResult = static::getConnection()->query("SELECT COUNT(*) as total FROM `{$table}`");
        $total = $totalResult->fetch_assoc()['total'];
        
        // Today's pending
        $today = date('Y-m-d');
        $todayResult = static::getConnection()->query("SELECT COUNT(*) as today FROM `{$table}` WHERE `scheduled_date` = '{$today}'");
        $todayCount = $todayResult->fetch_assoc()['today'];
        
        // Overdue pending
        $overdueResult = static::getConnection()->query("SELECT COUNT(*) as overdue FROM `{$table}` WHERE `scheduled_date` < '{$today}'");
        $overdueCount = $overdueResult->fetch_assoc()['overdue'];
        
        // By event type
        $eventTypeResult = static::getConnection()->query("SELECT `event_type`, COUNT(*) as count FROM `{$table}` GROUP BY `event_type`");
        $eventTypes = [];
        while ($row = $eventTypeResult->fetch_assoc()) {
            $eventTypes[$row['event_type']] = (int)$row['count'];
        }
        
        return [
            'total' => (int)$total,
            'today' => (int)$todayCount,
            'overdue' => (int)$overdueCount,
            'by_event_type' => $eventTypes
        ];
    }

    /**
     * Clean up old completed pending pins
     */
    public static function cleanupOldPending($daysOld = 30)
    {
        $cutoffDate = date('Y-m-d', strtotime("-{$daysOld} days"));
        $instance = new static();
        
        $sql = "DELETE FROM `{$instance->getTable()}` WHERE `scheduled_date` < ?";
        $stmt = static::getConnection()->prepare($sql);
        
        if (!$stmt) {
            return 0;
        }
        
        $stmt->bind_param('s', $cutoffDate);
        $stmt->execute();
        $affectedRows = static::getConnection()->affectedRows();
        $stmt->close();
        
        return $affectedRows;
    }

    /**
     * Get upcoming pending pins
     */
    public static function getUpcoming($days = 7)
    {
        $startDate = date('Y-m-d');
        $endDate = date('Y-m-d', strtotime("+{$days} days"));
        
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `scheduled_date` BETWEEN ? AND ? ORDER BY `scheduled_date` ASC";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('ss', $startDate, $endDate);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $models = [];
        while ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }
        
        $stmt->close();
        return $models;
    }
}
