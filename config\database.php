<?php

/**
 * Database Configuration
 * 
 * Configuration for database connections
 */

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work.
    |
    */

    'default' => 'wordpress',

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | We use the WordPress database connection by default.
    |
    */

    'connections' => [

        'wordpress' => [
            'driver' => 'mysql',
            'host' => pinboard_config('database.host', 'localhost'),
            'database' => pinboard_config('database.name', ''),
            'username' => pinboard_config('database.user', ''),
            'password' => pinboard_config('database.password', ''),
            'charset' => pinboard_config('database.charset', 'utf8mb4'),
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => 'InnoDB',
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'pinboard_migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => 'phpredis',

        'options' => [
            'cluster' => 'redis',
            'prefix' => 'pinboard_database_',
        ],

        'default' => [
            'url' => null,
            'host' => '127.0.0.1',
            'password' => null,
            'port' => 6379,
            'database' => 0,
        ],

        'cache' => [
            'url' => null,
            'host' => '127.0.0.1',
            'password' => null,
            'port' => 6379,
            'database' => 1,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Table Names
    |--------------------------------------------------------------------------
    |
    | Pin Board specific table names
    |
    */

    'tables' => [
        'pin_history' => 'wp_pin_history',
        'pin_details' => 'wp_pin_details',
        'pending_pins' => 'wp_pending_pins',
        'ignored_posts' => 'wp_ignored_posts',
    ],

];
