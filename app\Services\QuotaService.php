<?php

use App\Models\PinHistory;
use App\Models\PinDetail;
use App\Models\QuotaRule;

/**
 * Quota Service
 *
 * Handles pin quota calculations and management using ORM models
 */

/**
 * Calculate daily quota using advanced rule-based system
 */
function pinboard_calculate_advanced_quota($db = null, $today = null) {
    if (!$today) {
        $today = new DateTime('now', new DateTimeZone(pinboard_config('timezone', 'Asia/Dhaka')));
    }

    // Get account age and statistics for context
    $context = pinboard_get_quota_context($db, $today);

    // Calculate quota using rule-based system
    $quota_result = QuotaRule::calculateQuotaFromRules($context);

    // Get number of pins submitted today
    $submitted_today = pinboard_get_today_pin_count($db);

    // Calculate remaining quota
    $remaining_quota = $quota_result['total_quota'] - $submitted_today;
    if ($remaining_quota < 0) {
        $remaining_quota = 0;
    }

    return [
        'daily_quota' => $quota_result['total_quota'],
        'base_quota' => $quota_result['base_quota'],
        'bonus_quota' => $quota_result['bonus_quota'],
        'submitted_today' => $submitted_today,
        'remaining_quota' => $remaining_quota,
        'account_age_days' => $context['account_age_days'],
        'applied_rules' => $quota_result['applied_rules'],
        'context' => $context
    ];
}

/**
 * Get quota calculation context
 */
function pinboard_get_quota_context($db, $today) {
    // Get oldest pin date to calculate account age using ORM
    $histories = PinHistory::all();
    $oldest_date = null;

    foreach ($histories as $history) {
        if ($history->publish_date) {
            $date = strtotime($history->publish_date);
            if (!$oldest_date || $date < $oldest_date) {
                $oldest_date = $date;
            }
        }
    }

    $account_age_days = 0;
    if ($oldest_date) {
        $first_pin_date = new DateTime();
        $first_pin_date->setTimestamp($oldest_date);
        $interval = $first_pin_date->diff($today);
        $account_age_days = $interval->days;
    }

    // Get pin statistics for context
    $pins_today = pinboard_get_today_pin_count($db);
    $pins_this_week = pinboard_get_weekly_pin_stats($db);
    $pins_this_month = pinboard_get_monthly_pin_stats($db);
    $success_rate = pinboard_calculate_success_rate($db);

    return [
        'account_age_days' => $account_age_days,
        'pins_today' => $pins_today,
        'pins_this_week' => $pins_this_week,
        'pins_this_month' => $pins_this_month,
        'success_rate' => $success_rate,
        'day_of_week' => $today->format('N'),
        'month' => $today->format('n'),
        'hour' => $today->format('G')
    ];
}

/**
 * Calculate success rate based on pin history
 */
function pinboard_calculate_success_rate($db) {
    $tables = pinboard_config('tables');
    $thirty_days_ago = date('Y-m-d', strtotime('-30 days'));

    // Get total pins in last 30 days
    $total_query = "SELECT COUNT(*) as count FROM `{$tables['pin_details']}` WHERE pin_date >= '$thirty_days_ago'";
    $total_result = $db->query($total_query);
    $total_pins = 0;

    if ($total_result) {
        $row = $total_result->fetch_assoc();
        $total_pins = intval($row['count']);
        $total_result->free();
    }

    if ($total_pins === 0) {
        return 100; // Default to 100% for new accounts
    }

    // For now, assume 95% success rate (can be enhanced with actual success tracking)
    return 95;
}

/**
 * Calculate daily quota based on account age and current usage using ORM (Legacy function)
 */
function pinboard_calculate_quota($db = null, $today = null) {
    if (!$today) {
        $today = new DateTime('now', new DateTimeZone(pinboard_config('timezone', 'Asia/Dhaka')));
    }

    // Get oldest pin date to calculate account age using ORM
    $histories = PinHistory::all();
    $oldest_date = null;

    foreach ($histories as $history) {
        if ($history->publish_date) {
            $date = strtotime($history->publish_date);
            if (!$oldest_date || $date < $oldest_date) {
                $oldest_date = $date;
            }
        }
    }

    $days = 0;
    if ($oldest_date) {
        $first_pin_date = new DateTime();
        $first_pin_date->setTimestamp($oldest_date);
        $interval = $first_pin_date->diff($today);
        $days = $interval->days;
    }
    
    // Calculate initial quota based on account age
    $initial_quota = pinboard_config('quota.initial', 10);
    $thresholds = pinboard_config('quota.thresholds', [
        30 => 20,
        90 => 35,
        150 => 50
    ]);
    
    foreach ($thresholds as $threshold_days => $quota) {
        if ($days >= $threshold_days) {
            $initial_quota = $quota;
        }
    }
    
    // Get number of pins submitted today
    $submitted_today = pinboard_get_today_pin_count($db);
    
    // Calculate remaining quota
    $remaining_quota = $initial_quota - $submitted_today;
    if ($remaining_quota < 0) {
        $remaining_quota = 0;
    }
    
    return [
        'daily_quota' => $initial_quota,
        'submitted_today' => $submitted_today,
        'remaining_quota' => $remaining_quota,
        'account_age_days' => $days
    ];
}

/**
 * Get count of pins submitted today
 */
function pinboard_get_today_pin_count($db) {
    $tables = pinboard_config('tables');
    $today = current_time('Y-m-d');
    
    $query = "SELECT COUNT(*) as count FROM `{$tables['pin_details']}` WHERE pin_date = '$today'";
    $result = $db->query($query);
    
    if ($result) {
        $row = $result->fetch_assoc();
        $result->free();
        return intval($row['count']);
    }
    
    return 0;
}

/**
 * Get weekly pin statistics
 */
function pinboard_get_weekly_pin_stats($db) {
    $tables = pinboard_config('tables');
    $week_ago = date('Y-m-d', strtotime('-7 days'));
    
    $query = "SELECT COUNT(*) as count FROM `{$tables['pin_details']}` WHERE pin_date >= '$week_ago'";
    $result = $db->query($query);
    
    if ($result) {
        $row = $result->fetch_assoc();
        $result->free();
        return intval($row['count']);
    }
    
    return 0;
}

/**
 * Get monthly pin statistics
 */
function pinboard_get_monthly_pin_stats($db) {
    $tables = pinboard_config('tables');
    $first_day_of_month = date('Y-m-01');
    
    $query = "SELECT COUNT(*) as count FROM `{$tables['pin_details']}` WHERE pin_date >= '$first_day_of_month'";
    $result = $db->query($query);
    
    if ($result) {
        $row = $result->fetch_assoc();
        $result->free();
        return intval($row['count']);
    }
    
    return 0;
}

/**
 * Check if user can create new pins based on quota
 */
function pinboard_can_create_pin($db, $pin_type = 'new') {
    $today = new DateTime('now', new DateTimeZone(pinboard_config('timezone', 'Asia/Dhaka')));
    $quota_info = pinboard_calculate_quota($db, $today);
    
    if ($quota_info['remaining_quota'] <= 0) {
        return [
            'allowed' => false,
            'reason' => 'Daily quota exceeded',
            'quota_info' => $quota_info
        ];
    }
    
    // For new pins, check if we have quota for new pins specifically
    if ($pin_type === 'new') {
        $new_pin_percentage = pinboard_config('quota.new_pin_percentage', 0.4);
        $max_new_pins_today = ceil($quota_info['daily_quota'] * $new_pin_percentage);
        
        // Count new pins created today (pins where this is the first pin for the post)
        $new_pins_today = pinboard_get_new_pins_count_today($db);
        
        if ($new_pins_today >= $max_new_pins_today) {
            return [
                'allowed' => false,
                'reason' => 'New pin quota exceeded for today',
                'quota_info' => $quota_info,
                'new_pins_today' => $new_pins_today,
                'max_new_pins_today' => $max_new_pins_today
            ];
        }
    }
    
    return [
        'allowed' => true,
        'quota_info' => $quota_info
    ];
}

/**
 * Get count of new pins created today
 */
function pinboard_get_new_pins_count_today($db) {
    $tables = pinboard_config('tables');
    $today = current_time('Y-m-d');
    
    // Count pins where this is the first pin for the post (created today)
    $query = "SELECT COUNT(*) as count 
              FROM `{$tables['pin_details']}` d
              INNER JOIN `{$tables['pin_history']}` h ON d.history_id = h.id
              WHERE d.pin_date = '$today'
              AND (SELECT COUNT(*) FROM `{$tables['pin_details']}` d2 WHERE d2.history_id = h.id) = 1";
    
    $result = $db->query($query);
    
    if ($result) {
        $row = $result->fetch_assoc();
        $result->free();
        return intval($row['count']);
    }
    
    return 0;
}

/**
 * Get quota usage statistics
 */
function pinboard_get_quota_stats($db) {
    $today = new DateTime('now', new DateTimeZone(pinboard_config('timezone', 'Asia/Dhaka')));
    $quota_info = pinboard_calculate_quota($db, $today);
    
    $stats = [
        'today' => $quota_info,
        'weekly' => pinboard_get_weekly_pin_stats($db),
        'monthly' => pinboard_get_monthly_pin_stats($db),
        'new_pins_today' => pinboard_get_new_pins_count_today($db),
        'usage_percentage' => 0
    ];
    
    if ($quota_info['daily_quota'] > 0) {
        $stats['usage_percentage'] = round(
            ($quota_info['submitted_today'] / $quota_info['daily_quota']) * 100, 
            1
        );
    }
    
    return $stats;
}

/**
 * Advanced Quota Management Functions
 */

/**
 * Get advanced quota statistics with rule breakdown
 */
function pinboard_get_advanced_quota_stats($db) {
    $today = new DateTime('now', new DateTimeZone(pinboard_config('timezone', 'Asia/Dhaka')));
    $advanced_quota = pinboard_calculate_advanced_quota($db, $today);
    $legacy_quota = pinboard_calculate_quota($db, $today);

    $stats = [
        'advanced' => $advanced_quota,
        'legacy' => $legacy_quota,
        'weekly' => pinboard_get_weekly_pin_stats($db),
        'monthly' => pinboard_get_monthly_pin_stats($db),
        'new_pins_today' => pinboard_get_new_pins_count_today($db),
        'usage_percentage' => 0,
        'efficiency_score' => 0
    ];

    if ($advanced_quota['daily_quota'] > 0) {
        $stats['usage_percentage'] = round(
            ($advanced_quota['submitted_today'] / $advanced_quota['daily_quota']) * 100,
            1
        );
    }

    // Calculate efficiency score based on quota utilization and success rate
    $efficiency_factors = [
        'quota_utilization' => min($stats['usage_percentage'] / 100, 1.0) * 0.4,
        'success_rate' => ($advanced_quota['context']['success_rate'] / 100) * 0.6
    ];

    $stats['efficiency_score'] = round(
        array_sum($efficiency_factors) * 100,
        1
    );

    return $stats;
}

/**
 * Check if user can create new pins with advanced quota system
 */
function pinboard_can_create_pin_advanced($db, $pin_type = 'new') {
    $today = new DateTime('now', new DateTimeZone(pinboard_config('timezone', 'Asia/Dhaka')));
    $quota_info = pinboard_calculate_advanced_quota($db, $today);

    if ($quota_info['remaining_quota'] <= 0) {
        return [
            'allowed' => false,
            'reason' => 'Daily quota exceeded',
            'quota_info' => $quota_info,
            'suggestions' => pinboard_get_quota_suggestions($quota_info)
        ];
    }

    // For new pins, check if we have quota for new pins specifically
    if ($pin_type === 'new') {
        $new_pin_percentage = pinboard_config('quota.new_pin_percentage', 0.4);
        $max_new_pins_today = ceil($quota_info['daily_quota'] * $new_pin_percentage);

        // Count new pins created today
        $new_pins_today = pinboard_get_new_pins_count_today($db);

        if ($new_pins_today >= $max_new_pins_today) {
            return [
                'allowed' => false,
                'reason' => 'New pin quota exceeded for today',
                'quota_info' => $quota_info,
                'new_pins_today' => $new_pins_today,
                'max_new_pins_today' => $max_new_pins_today,
                'suggestions' => pinboard_get_quota_suggestions($quota_info)
            ];
        }
    }

    return [
        'allowed' => true,
        'quota_info' => $quota_info,
        'optimization_tips' => pinboard_get_optimization_tips($quota_info)
    ];
}

/**
 * Get quota suggestions for improving limits
 */
function pinboard_get_quota_suggestions($quota_info) {
    $suggestions = [];

    $account_age = $quota_info['account_age_days'];
    $success_rate = $quota_info['context']['success_rate'];

    if ($account_age < 30) {
        $suggestions[] = [
            'type' => 'age_improvement',
            'message' => 'Your quota will increase to 20 pins/day after 30 days of activity',
            'days_remaining' => 30 - $account_age
        ];
    } elseif ($account_age < 90) {
        $suggestions[] = [
            'type' => 'age_improvement',
            'message' => 'Your quota will increase to 35 pins/day after 90 days of activity',
            'days_remaining' => 90 - $account_age
        ];
    } elseif ($account_age < 150) {
        $suggestions[] = [
            'type' => 'age_improvement',
            'message' => 'Your quota will increase to 50 pins/day after 150 days of activity',
            'days_remaining' => 150 - $account_age
        ];
    }

    if ($success_rate < 90) {
        $suggestions[] = [
            'type' => 'performance_improvement',
            'message' => 'Improve your success rate to 90%+ to unlock bonus quota',
            'current_rate' => $success_rate,
            'target_rate' => 90
        ];
    }

    $day_of_week = date('N');
    if ($day_of_week < 6) {
        $suggestions[] = [
            'type' => 'timing_optimization',
            'message' => 'Weekend bonus: +5 pins available on Saturday and Sunday',
            'bonus_available' => 'weekends'
        ];
    }

    return $suggestions;
}

/**
 * Get optimization tips for better quota utilization
 */
function pinboard_get_optimization_tips($quota_info) {
    $tips = [];

    $usage_percentage = ($quota_info['submitted_today'] / $quota_info['daily_quota']) * 100;

    if ($usage_percentage < 50) {
        $tips[] = [
            'type' => 'utilization',
            'message' => 'You have unused quota capacity. Consider pinning more content today.',
            'remaining_pins' => $quota_info['remaining_quota']
        ];
    } elseif ($usage_percentage > 90) {
        $tips[] = [
            'type' => 'planning',
            'message' => 'You are near your daily limit. Plan your remaining pins carefully.',
            'remaining_pins' => $quota_info['remaining_quota']
        ];
    }

    if (count($quota_info['applied_rules']) > 1) {
        $bonus_rules = array_filter($quota_info['applied_rules'], function($rule) {
            return $rule['type'] !== 'age_based';
        });

        if (!empty($bonus_rules)) {
            $tips[] = [
                'type' => 'bonus_active',
                'message' => 'You have active bonus quota from: ' . implode(', ', array_column($bonus_rules, 'rule_name')),
                'bonus_amount' => $quota_info['bonus_quota']
            ];
        }
    }

    return $tips;
}
