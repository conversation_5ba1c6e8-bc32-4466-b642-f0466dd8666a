<?php

/**
 * Pin Board Configuration
 * 
 * Configuration settings for the Pin Board application
 */

return [
    
    /*
    |--------------------------------------------------------------------------
    | Pin Quota Configuration
    |--------------------------------------------------------------------------
    |
    | These settings control the daily pin quotas based on account age
    |
    */
    'quota' => [
        'initial' => 10,
        'thresholds' => [
            30 => 20,   // After 30 days
            90 => 35,   // After 90 days
            150 => 50,  // After 150 days
        ],
        'new_pin_percentage' => 0.4, // 40% of quota for new pins

        // Advanced quota management settings
        'advanced_mode' => true, // Enable rule-based quota system
        'rule_based_calculation' => true,
        'performance_tracking' => true,
        'bonus_rules_enabled' => true,

        // Quota adjustment limits
        'max_daily_quota' => 100,
        'min_daily_quota' => 5,
        'max_bonus_quota' => 25,

        // Performance thresholds
        'high_performance_threshold' => 90, // Success rate %
        'low_performance_threshold' => 70,  // Success rate %

        // Seasonal adjustments
        'weekend_bonus' => 5,
        'holiday_bonus' => 10,
        'peak_season_multiplier' => 1.2,
    ],

    /*
    |--------------------------------------------------------------------------
    | Pin Reset Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for automatic pin reset functionality
    |
    */
    'reset' => [
        'days_threshold' => 151, // Reset pins older than 151 days
    ],

    /*
    |--------------------------------------------------------------------------
    | Email Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for email reporting system
    |
    */
    'email' => [
        'smtp' => [
            'host' => 'smtp.gmail.com',
            'port' => 587,
            'username' => '<EMAIL>',
            'password' => 'wlpa xveq kjjv cldy', // App password
            'encryption' => 'tls',
        ],
        'from' => [
            'email' => '<EMAIL>',
            'name' => 'Pin Board System',
        ],
        'recipients' => [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Post Filtering Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for filtering posts (listicles, etc.)
    |
    */
    'filtering' => [
        'listicle_patterns' => [
            '/\d+/', // Contains numbers
            '/list/i', // Contains "list" (case insensitive)
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | API Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for API endpoints and Chrome extension integration
    |
    */
    'api' => [
        'chrome_extension' => [
            'endpoint' => 'today_suggestions',
            'rate_limit' => 100, // Requests per hour
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Table Names
    |--------------------------------------------------------------------------
    |
    | Custom table names for pin board functionality
    |
    */
    'tables' => [
        'pin_history' => 'wp_pin_history',
        'pin_details' => 'wp_pin_details',
        'pending_pins' => 'wp_pending_pins',
        'ignored_posts' => 'wp_ignored_posts',
    ],

    /*
    |--------------------------------------------------------------------------
    | WordPress Integration
    |--------------------------------------------------------------------------
    |
    | Settings for WordPress integration
    |
    */
    'wordpress' => [
        'required_capability' => 'publish_posts',
        'post_types' => ['post'],
        'post_status' => ['publish'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Timezone Configuration
    |--------------------------------------------------------------------------
    |
    | Default timezone for the application
    |
    */
    'timezone' => 'Asia/Dhaka',

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for application logging
    |
    */
    'logging' => [
        'enabled' => true,
        'level' => 'info', // debug, info, warning, error
        'file' => 'pinboard.log',
    ],

];
