<?php

require_once __DIR__ . '/BaseController.php';

/**
 * Pin Controller
 * 
 * Handles pin-related API endpoints
 */
class PinController extends BaseController
{
    /**
     * Get all pins with pagination
     * GET /api/pins
     */
    public function index()
    {
        try {
            $this->logAction('index');
            
            $pagination = $this->getPagination();
            $result = PinManager::getAllPins($pagination['page'], $pagination['per_page']);
            
            if (!$result['success']) {
                return $this->error($result['error']);
            }
            
            return $this->success($result['pins'], 'Pins retrieved successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'index');
        }
    }

    /**
     * Get pin by post ID
     * GET /api/pins/{post_id}
     */
    public function show($postId)
    {
        try {
            $this->logAction('show', ['post_id' => $postId]);
            
            if (!$postId) {
                return $this->error('Post ID is required');
            }
            
            $result = PinManager::getPinByPostId($postId);
            
            if (!$result['success']) {
                return $this->error($result['error'], 404);
            }
            
            return $this->success($result, 'Pin retrieved successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'show');
        }
    }

    /**
     * Create a new pin
     * POST /api/pins
     */
    public function store()
    {
        try {
            if ($this->checkMethod('POST') !== true) return;
            
            $input = $this->sanitizeInput($this->getInput());
            $this->logAction('store', $input);
            
            $validation = $this->validateRequired($input, ['post_id', 'title', 'url']);
            if (!$validation['valid']) {
                return $this->error($validation['message'], 400);
            }
            
            $result = PinManager::createPin(
                $input['post_id'],
                $input['title'],
                $input['url'],
                $input['pin_link'] ?? null,
                $input['publish_date'] ?? null,
                $input['pin_date'] ?? null
            );
            
            if (!$result['success']) {
                return $this->error($result['error']);
            }
            
            return $this->success($result, 'Pin created successfully', 201);
            
        } catch (Exception $e) {
            return $this->handleException($e, 'store');
        }
    }

    /**
     * Update a pin
     * PUT /api/pins/{post_id}
     */
    public function update($postId)
    {
        try {
            if ($this->checkMethod('PUT') !== true) return;
            
            $input = $this->sanitizeInput($this->getInput());
            $this->logAction('update', ['post_id' => $postId, 'data' => $input]);
            
            if (!$postId) {
                return $this->error('Post ID is required');
            }
            
            $result = PinManager::updatePin($postId, $input);
            
            if (!$result['success']) {
                return $this->error($result['error'], 404);
            }
            
            return $this->success($result, 'Pin updated successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'update');
        }
    }

    /**
     * Delete a pin
     * DELETE /api/pins/{post_id}
     */
    public function destroy($postId)
    {
        try {
            if ($this->checkMethod('DELETE') !== true) return;
            
            $this->logAction('destroy', ['post_id' => $postId]);
            
            if (!$postId) {
                return $this->error('Post ID is required');
            }
            
            $result = PinManager::deletePin($postId);
            
            if (!$result['success']) {
                return $this->error($result['error'], 404);
            }
            
            return $this->success(null, 'Pin deleted successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'destroy');
        }
    }

    /**
     * Add pin detail to existing pin
     * POST /api/pins/{post_id}/details
     */
    public function addDetail($postId)
    {
        try {
            if ($this->checkMethod('POST') !== true) return;
            
            $input = $this->sanitizeInput($this->getInput());
            $this->logAction('addDetail', ['post_id' => $postId, 'data' => $input]);
            
            if (!$postId) {
                return $this->error('Post ID is required');
            }
            
            $validation = $this->validateRequired($input, ['pin_link']);
            if (!$validation['valid']) {
                return $this->error($validation['message'], 400);
            }
            
            $result = PinManager::addPinDetail(
                $postId,
                $input['pin_link'],
                $input['pin_date'] ?? null
            );
            
            if (!$result['success']) {
                return $this->error($result['error']);
            }
            
            return $this->success($result, 'Pin detail added successfully', 201);
            
        } catch (Exception $e) {
            return $this->handleException($e, 'addDetail');
        }
    }

    /**
     * Search pins
     * GET /api/pins/search?q={query}
     */
    public function search()
    {
        try {
            $query = $this->getQuery();
            $this->logAction('search', $query);
            
            if (!isset($query['q']) || empty($query['q'])) {
                return $this->error('Search query is required');
            }
            
            $limit = isset($query['limit']) ? min(100, max(1, intval($query['limit']))) : 20;
            
            $result = PinManager::searchPins($query['q'], $limit);
            
            if (!$result['success']) {
                return $this->error($result['error']);
            }
            
            return $this->success($result, 'Search completed successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'search');
        }
    }

    /**
     * Get pin statistics
     * GET /api/pins/statistics
     */
    public function statistics()
    {
        try {
            $this->logAction('statistics');
            
            $result = PinManager::getPinStatistics();
            
            if (!$result['success']) {
                return $this->error($result['error']);
            }
            
            return $this->success($result['statistics'], 'Statistics retrieved successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'statistics');
        }
    }

    /**
     * Bulk ignore posts
     * POST /api/pins/bulk-ignore
     */
    public function bulkIgnore()
    {
        try {
            if ($this->checkMethod('POST') !== true) return;
            
            $input = $this->sanitizeInput($this->getInput());
            $this->logAction('bulkIgnore', $input);
            
            $validation = $this->validateRequired($input, ['post_ids']);
            if (!$validation['valid']) {
                return $this->error($validation['message'], 400);
            }
            
            if (!is_array($input['post_ids'])) {
                return $this->error('post_ids must be an array');
            }
            
            $result = PinManager::bulkIgnorePosts($input['post_ids']);
            
            if (!$result['success']) {
                return $this->error($result['error']);
            }
            
            return $this->success($result, 'Posts ignored successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'bulkIgnore');
        }
    }

    /**
     * Clean up old data
     * POST /api/pins/cleanup
     */
    public function cleanup()
    {
        try {
            if ($this->checkMethod('POST') !== true) return;
            
            $input = $this->getInput();
            $this->logAction('cleanup', $input);
            
            $daysOld = isset($input['days_old']) ? max(30, intval($input['days_old'])) : 365;
            
            $result = PinManager::cleanupOldData($daysOld);
            
            if (!$result['success']) {
                return $this->error($result['error']);
            }
            
            return $this->success($result, 'Cleanup completed successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'cleanup');
        }
    }
}
