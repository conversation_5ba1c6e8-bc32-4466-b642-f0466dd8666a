<?php
session_start();

// Try to find WordPress config file - check multiple possible locations
$wp_config_paths = array(
    '../../wp-config.php',  // Two levels up (for pin-reminder subdirectory)
    '../wp-config.php',     // One level up (original assumption)
    './wp-config.php'       // Same directory
);

$wp_config_found = false;
foreach ($wp_config_paths as $config_path) {
    if (file_exists($config_path)) {
        require_once($config_path);
        $wp_config_found = true;
        break;
    }
}

if (!$wp_config_found) {
    die('WordPress configuration file not found. Please ensure this script is in the correct directory.');
}

// Prevent direct file access
if (!defined('ABSPATH')) {
    $wp_load_paths = array(
        '../../wp-load.php',  // Two levels up (for pin-reminder subdirectory)
        '../wp-load.php',     // One level up (original assumption)
        './wp-load.php'       // Same directory
    );

    $wp_load_found = false;
    foreach ($wp_load_paths as $load_path) {
        if (file_exists($load_path)) {
            require_once($load_path);
            $wp_load_found = true;
            break;
        }
    }

    if (!$wp_load_found) {
        die('WordPress load file not found. Please ensure this script is in the correct directory.');
    }
}

// Public API Endpoint: Get today's pin suggestions for Chrome extension (no authentication required)
if (isset($_GET['api']) && $_GET['api'] === 'today_suggestions') {
    // Set PHP timezone to match WordPress time zone
    date_default_timezone_set('Asia/Dhaka');

    // Database connection using wp-config.php credentials
    $db_connection = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($db_connection->connect_error) {
        header('Content-Type: application/json');
        echo json_encode(array('success' => false, 'error' => 'Database connection failed'));
        exit;
    }

    // Create tables if they don't exist (reuse existing function)
    create_pin_tables();

    $today_str = current_time('Y-m-d');
    $today = new DateTime($today_str, new DateTimeZone('Asia/Dhaka'));

    // Query posts (reuse existing logic)
    $args = array(
        'post_status'    => 'publish',
        'posts_per_page' => -1,
        'orderby'        => 'date',
        'order'          => 'DESC',
        'cache_results'  => true
    );
    $query = new WP_Query($args);
    $posts = $query->posts;

    // Load data using existing functions
    $pin_history = load_pin_history();
    $ignored_posts = load_ignored_posts();
    $new_pin_candidates = get_new_pin_candidates($posts, $pin_history, $today_str, $ignored_posts);
    $prioritized_candidates = prioritize_new_pin_candidates($new_pin_candidates);
    $repin_candidates = get_repin_candidates($posts, $pin_history, $today_str, $ignored_posts);

    // Calculate daily quota (reuse existing logic)
    $oldest_date_query = "SELECT MIN(publish_date) as oldest_date FROM " . DB_NAME . ".wp_pin_history";
    $result = $db_connection->query($oldest_date_query);
    $row = $result->fetch_assoc();
    $oldest_date = strtotime($row['oldest_date']);
    $days = 0;
    if ($oldest_date) {
        $first_pin_date = new DateTime();
        $first_pin_date->setTimestamp($oldest_date);
        $interval = $first_pin_date->diff($today);
        $days = $interval->days;
    }
    $initial_quota = 10;
    if ($days >= 150) {
        $initial_quota = 50;
    } elseif ($days >= 90) {
        $initial_quota = 35;
    } elseif ($days >= 30) {
        $initial_quota = 20;
    }

    // Get number of pins submitted today and calculate remaining quota
    $submitted_today = get_today_pin_count();
    $daily_quota = $initial_quota - $submitted_today;
    if ($daily_quota < 0) {
        $daily_quota = 0;
    }

    // Apply quota limits (reuse existing logic)
    $max_new_pins = min(ceil($daily_quota * 0.4), count($prioritized_candidates));
    $max_repins = min($daily_quota - $max_new_pins, count($repin_candidates));

    // Format API response - only Title and URL as requested
    $response = array(
        'success' => true,
        'date' => $today_str,
        'quota_info' => array(
            'daily_quota' => $daily_quota,
            'submitted_today' => $submitted_today,
            'account_age_days' => $days
        ),
        'new_pins' => array(),
        're_pins' => array()
    );

    // Add new pins (limited by quota)
    for ($i = 0; $i < $max_new_pins; $i++) {
        if (isset($prioritized_candidates[$i])) {
            $response['new_pins'][] = array(
                'title' => $prioritized_candidates[$i]['title'],
                'url' => $prioritized_candidates[$i]['url']
            );
        }
    }

    // Add re-pins (limited by quota)
    for ($i = 0; $i < $max_repins; $i++) {
        if (isset($repin_candidates[$i])) {
            $response['re_pins'][] = array(
                'title' => $repin_candidates[$i]['title'],
                'url' => $repin_candidates[$i]['url']
            );
        }
    }

    // Close database connection
    $db_connection->close();

    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Check if user is logged in and has at least Author level permissions (for main dashboard only, not API)
if (!isset($_GET['api']) && (!is_user_logged_in() || !current_user_can('publish_posts'))) {
    wp_die(
        'You do not have sufficient permissions to access this page.',
        'Access Denied',
        array('response' => 403)
    );
}

// Set PHP timezone to match WordPress time zone
date_default_timezone_set('Asia/Dhaka');

// Database connection using wp-config.php credentials
$db_connection = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
if ($db_connection->connect_error) {
    die("Connection failed: " . $db_connection->connect_error);
}

// Create necessary database tables (including a new table for ignored posts)
function create_pin_tables() {
    global $db_connection;

    $tables = array(
        "wp_pin_history" => "CREATE TABLE IF NOT EXISTS " . DB_NAME . ".wp_pin_history (
            id bigint(20) AUTO_INCREMENT,
            post_id bigint(20) NOT NULL UNIQUE,
            title varchar(255),
            url varchar(255),
            publish_date date,
            PRIMARY KEY (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        "wp_pin_details" => "CREATE TABLE IF NOT EXISTS " . DB_NAME . ".wp_pin_details (
            id bigint(20) AUTO_INCREMENT,
            history_id bigint(20) NOT NULL,
            pin_link varchar(255),
            pin_date date,
            PRIMARY KEY (id),
            KEY history_id (history_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        "wp_pending_pins" => "CREATE TABLE IF NOT EXISTS " . DB_NAME . ".wp_pending_pins (
            id bigint(20) AUTO_INCREMENT,
            post_id bigint(20) NOT NULL,
            event_type varchar(10),
            scheduled_date date,
            retry_count int DEFAULT 0,
            PRIMARY KEY (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
        "wp_ignored_posts" => "CREATE TABLE IF NOT EXISTS " . DB_NAME . ".wp_ignored_posts (
            post_id bigint(20) NOT NULL UNIQUE,
            ignored_date date,
            PRIMARY KEY (post_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;"
    );

    foreach ($tables as $table_name => $sql) {
        if (!$db_connection->query($sql)) {
            die("Error creating table $table_name: " . $db_connection->error);
        }
    }
}

// Create tables if they don't exist
create_pin_tables();

// Email Configuration Constants - TEMPORARY GMAIL TESTING
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'wlpa xveq kjjv cldy');
define('SMTP_ENCRYPTION', 'tls');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Pin Board System');
define('TO_EMAILS', '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>');

// Email Configuration Function
function get_email_config() {
    return array(
        'smtp_host' => SMTP_HOST,
        'smtp_port' => SMTP_PORT,
        'smtp_username' => SMTP_USERNAME,
        'smtp_password' => SMTP_PASSWORD,
        'smtp_encryption' => SMTP_ENCRYPTION,
        'from_email' => FROM_EMAIL,
        'from_name' => FROM_NAME,
        'to_emails' => explode(',', TO_EMAILS)
    );
}

// Get Today's Pin Report Data
function get_today_pin_report_data() {
    // Create new database connection for email functionality
    $email_db_connection = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($email_db_connection->connect_error) {
        error_log("Email database connection failed: " . $email_db_connection->connect_error);
        return array(
            'date' => current_time('Y-m-d'),
            'total_submissions' => 0,
            'new_pins' => array(),
            're_pins' => array(),
            'new_pins_count' => 0,
            're_pins_count' => 0,
            'not_pinned_count' => 0,
            'all_submissions' => array()
        );
    }

    $today = current_time('Y-m-d');

    // Get today's submissions with post details and pin counts
    $query = "SELECT h.post_id, h.title, h.url, d.pin_link, d.pin_date,
                     (SELECT COUNT(*) FROM " . DB_NAME . ".wp_pin_details d2 WHERE d2.history_id = h.id) as total_pins
              FROM " . DB_NAME . ".wp_pin_history h
              INNER JOIN " . DB_NAME . ".wp_pin_details d ON h.id = d.history_id
              WHERE d.pin_date = '$today'
              ORDER BY d.pin_date DESC, h.title ASC";

    $result = $email_db_connection->query($query);
    $submissions = array();
    $new_pins = array();
    $re_pins = array();

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $submissions[] = $row;

            // Classify as new pin or re-pin
            if ($row['total_pins'] == 1) {
                $new_pins[] = $row;
            } else {
                $re_pins[] = $row;
            }
        }
        $result->free();
    }

    // Get not pinned count from existing function
    $args = array(
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'orderby' => 'date',
        'order' => 'DESC',
        'cache_results' => true
    );
    $query_posts = new WP_Query($args);
    $posts = $query_posts->posts;

    $pin_history = load_pin_history();
    $ignored_posts = load_ignored_posts();
    $status_counts = get_posts_by_status($posts, $pin_history, $ignored_posts);

    // Close the email database connection
    $email_db_connection->close();

    return array(
        'date' => $today,
        'total_submissions' => count($submissions),
        'new_pins' => $new_pins,
        're_pins' => $re_pins,
        'new_pins_count' => count($new_pins),
        're_pins_count' => count($re_pins),
        'not_pinned_count' => $status_counts[0],
        'all_submissions' => $submissions
    );
}

// New function: Get count of pins submitted today
function get_today_pin_count() {
    global $db_connection;
    $today = current_time('Y-m-d');
    $query = "SELECT COUNT(*) as count FROM " . DB_NAME . ".wp_pin_details WHERE pin_date = '$today'";
    $result = $db_connection->query($query);
    if ($result) {
        $row = $result->fetch_assoc();
        $result->free();
        return intval($row['count']);
    }
    return 0;
}

// Generate HTML Email Template
function generate_email_html($report_data) {
    $date = date('F j, Y', strtotime($report_data['date']));
    $formatted_date = date('l, F j, Y', strtotime($report_data['date']));

    // Get the WordPress site domain
    $site_url = get_site_url();
    $domain = parse_url($site_url, PHP_URL_HOST);

    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Daily Pin Report - ' . $date . '</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f5f7fa; }
            .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .header h1 { margin: 0; font-size: 28px; color: white !important; }
            .header h1 * { color: white !important; }
            .header p { margin: 10px 0 0 0; opacity: 0.9; }
            .content { padding: 30px; }
            .summary { background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 30px; }
            .summary h2 { margin-top: 0; color: #495057; }
            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px; }
            .stat-item { text-align: center; padding: 15px; background: white; border-radius: 6px; border: 1px solid #dee2e6; }
            .stat-number { font-size: 24px; font-weight: bold; color: #007bff; }
            .stat-label { font-size: 12px; color: #6c757d; margin-top: 5px; }
            .stat-item.not-pinned { background: #dc3545; border-color: #dc3545; }
            .stat-item.not-pinned .stat-number { color: white; font-weight: bold; }
            .stat-item.not-pinned .stat-label { color: white; font-weight: bold; }
            .section { margin-bottom: 30px; }
            .section h3 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
            table { width: 100%; border-collapse: collapse; margin-top: 15px; }
            th, td { padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
            th { background-color: #f8f9fa; font-weight: 600; color: #495057; }
            tr:hover { background-color: #f8f9fa; }
            .pin-link { color: #007bff; text-decoration: none; }
            .pin-link:hover { text-decoration: underline; }
            .badge { padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 500; }
            .badge-new { background: #d4edda; color: #155724; }
            .badge-repin { background: #fff3cd; color: #856404; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 12px; border-radius: 0 0 8px 8px; }
            .no-data { text-align: center; padding: 40px; color: #6c757d; font-style: italic; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>📌 Daily Pin Report - ' . $domain . '</h1>
                <p>' . $formatted_date . '</p>
            </div>

            <div class="content">
                <div class="summary">
                    <h2>📊 Summary Statistics</h2>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">' . $report_data['total_submissions'] . '</div>
                            <div class="stat-label">Total Pins Today</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">' . $report_data['new_pins_count'] . '</div>
                            <div class="stat-label">New Pins</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">' . $report_data['re_pins_count'] . '</div>
                            <div class="stat-label">Re-pins</div>
                        </div>
                        <div class="stat-item not-pinned">
                            <div class="stat-number">' . $report_data['not_pinned_count'] . '</div>
                            <div class="stat-label">Not Pinned</div>
                        </div>
                    </div>
                </div>';

    // New Pins Section
    if (!empty($report_data['new_pins'])) {
        $html .= '
                <div class="section">
                    <h3>🆕 New Pins Today (' . count($report_data['new_pins']) . ')</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Post Title</th>
                                <th>Pin Link</th>
                            </tr>
                        </thead>
                        <tbody>';

        foreach ($report_data['new_pins'] as $pin) {
            $html .= '
                            <tr>
                                <td><a href="' . esc_url($pin['url']) . '" style="color: #333; text-decoration: none;">' . esc_html($pin['title']) . '</a></td>
                                <td><a href="' . esc_url($pin['pin_link']) . '" class="pin-link" target="_blank">View Pin</a></td>
                            </tr>';
        }

        $html .= '
                        </tbody>
                    </table>
                </div>';
    }

    // Re-pins Section
    if (!empty($report_data['re_pins'])) {
        $html .= '
                <div class="section">
                    <h3>🔄 Re-pins Today (' . count($report_data['re_pins']) . ')</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Post Title</th>
                                <th>Pin Link</th>
                                <th>Total Pins</th>
                            </tr>
                        </thead>
                        <tbody>';

        foreach ($report_data['re_pins'] as $pin) {
            $html .= '
                            <tr>
                                <td><a href="' . esc_url($pin['url']) . '" style="color: #333; text-decoration: none;">' . esc_html($pin['title']) . '</a></td>
                                <td><a href="' . esc_url($pin['pin_link']) . '" class="pin-link" target="_blank">View Pin</a></td>
                                <td><span class="badge badge-repin">' . $pin['total_pins'] . ' pins</span></td>
                            </tr>';
        }

        $html .= '
                        </tbody>
                    </table>
                </div>';
    }

    // No submissions message
    if (empty($report_data['new_pins']) && empty($report_data['re_pins'])) {
        $html .= '
                <div class="no-data">
                    <p>No pins were submitted today.</p>
                </div>';
    }

    $html .= '
            </div>

            <div class="footer">
                <p>Generated by Pin Board System</p>
                <p>This is an automated report. Please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>';

    return $html;
}

// Send Pin Report Email
function send_pin_report_email() {
    try {
        // Get report data
        $report_data = get_today_pin_report_data();

        // Get email configuration
        $email_config = get_email_config();

        // Generate email content
        $html_content = generate_email_html($report_data);
        $subject = 'Daily Pin Report - ' . date('F j, Y', strtotime($report_data['date']));

        // Prepare headers for HTML email
        $headers = array();
        $headers[] = 'MIME-Version: 1.0';
        $headers[] = 'Content-type: text/html; charset=UTF-8';
        $headers[] = 'From: ' . $email_config['from_name'] . ' <' . $email_config['from_email'] . '>';
        $headers[] = 'Reply-To: ' . $email_config['from_email'];
        $headers[] = 'X-Mailer: PHP/' . phpversion();

        // Configure SMTP settings for wp_mail
        add_action('phpmailer_init', function($phpmailer) use ($email_config) {
            $phpmailer->isSMTP();
            $phpmailer->Host = $email_config['smtp_host'];
            $phpmailer->SMTPAuth = true;
            $phpmailer->Port = $email_config['smtp_port'];
            $phpmailer->Username = $email_config['smtp_username'];
            $phpmailer->Password = $email_config['smtp_password'];

            if ($email_config['smtp_encryption'] === 'ssl') {
                $phpmailer->SMTPSecure = 'ssl';
            } elseif ($email_config['smtp_encryption'] === 'tls') {
                $phpmailer->SMTPSecure = 'tls';
            }

            $phpmailer->SMTPDebug = 0; // Set to 2 for debugging
        });

        $success_count = 0;
        $errors = array();

        // Send email to each recipient
        foreach ($email_config['to_emails'] as $to_email) {
            $to_email = trim($to_email);
            if (!empty($to_email)) {
                $result = wp_mail($to_email, $subject, $html_content, $headers);
                if ($result) {
                    $success_count++;
                } else {
                    $errors[] = "Failed to send email to: $to_email";
                }
            }
        }

        // Remove the phpmailer_init action to avoid conflicts
        remove_all_actions('phpmailer_init');

        if ($success_count > 0) {
            return array(
                'success' => true,
                'message' => "Email sent successfully to $success_count recipient(s).",
                'sent_count' => $success_count,
                'total_submissions' => $report_data['total_submissions'],
                'new_pins' => $report_data['new_pins_count'],
                're_pins' => $report_data['re_pins_count'],
                'not_pinned' => $report_data['not_pinned_count']
            );
        } else {
            return array(
                'success' => false,
                'message' => 'Failed to send email to any recipients.',
                'errors' => $errors
            );
        }

    } catch (Exception $e) {
        error_log('Email sending error: ' . $e->getMessage());
        return array(
            'success' => false,
            'message' => 'Error sending email: ' . $e->getMessage()
        );
    }
}

// New function: Get count of posts by status (0-5)
function get_posts_by_status($posts, $pin_history, $ignored_posts) {
    $status_counts = array(
        0 => 0, // Not Pinned
        1 => 0, // 1 Pin Completed
        2 => 0, // 2 Pins Completed
        3 => 0, // 3 Pins Completed
        4 => 0, // 4 Pins Completed
        5 => 0  // Complete (5 Pins)
    );

    foreach ($posts as $post) {
        $post_id = $post->ID;
        if (in_array($post_id, $ignored_posts)) continue;

        $current_status = 0;
        if (isset($pin_history[$post_id]) && !empty($pin_history[$post_id]['pins'])) {
            $current_status = count($pin_history[$post_id]['pins']);
            if ($current_status > 5) $current_status = 5;
        }

        $status_counts[$current_status]++;
    }

    return $status_counts;
}

// New function: Get total posts count (excluding ignored)
function get_total_posts_count($posts, $ignored_posts) {
    $total = 0;
    foreach ($posts as $post) {
        if (!in_array($post->ID, $ignored_posts)) {
            $total++;
        }
    }
    return $total;
}

// New function: Get completion percentage
function get_completion_percentage($status_counts) {
    $total_posts = array_sum($status_counts);
    if ($total_posts == 0) return 0;

    $completed_posts = $status_counts[5]; // Only fully completed (5 pins)
    return round(($completed_posts / $total_posts) * 100, 1);
}

// New function: Get progress percentage (posts with any progress)
function get_progress_percentage($status_counts) {
    $total_posts = array_sum($status_counts);
    if ($total_posts == 0) return 0;

    $posts_with_progress = $status_counts[1] + $status_counts[2] + $status_counts[3] + $status_counts[4] + $status_counts[5];
    return round(($posts_with_progress / $total_posts) * 100, 1);
}

// New function: Get posts with any progress (status 1-5)
function get_posts_with_progress($status_counts) {
    return $status_counts[1] + $status_counts[2] + $status_counts[3] + $status_counts[4] + $status_counts[5];
}

// New function: Get weekly pin statistics
function get_weekly_pin_stats() {
    global $db_connection;
    $week_ago = date('Y-m-d', strtotime('-7 days'));
    $query = "SELECT COUNT(*) as count FROM " . DB_NAME . ".wp_pin_details WHERE pin_date >= '$week_ago'";
    $result = $db_connection->query($query);
    if ($result) {
        $row = $result->fetch_assoc();
        $result->free();
        return intval($row['count']);
    }
    return 0;
}

// New function: Get monthly pin statistics
function get_monthly_pin_stats() {
    global $db_connection;
    $first_day_of_month = date('Y-m-01'); // First day of current month
    $query = "SELECT COUNT(*) as count FROM " . DB_NAME . ".wp_pin_details WHERE pin_date >= '$first_day_of_month'";
    $result = $db_connection->query($query);
    if ($result) {
        $row = $result->fetch_assoc();
        $result->free();
        return intval($row['count']);
    }
    return 0;
}

// New function: Get pending posts count (status 0)
function get_pending_posts_count($status_counts) {
    return $status_counts[0];
}

// Reset pins for posts where first pin is 151+ days old
function reset_expired_pins() {
    global $db_connection;
    $today = date('Y-m-d');
    $reset_date = date('Y-m-d', strtotime('-151 days'));

    // Find posts with first pin older than 151 days
    $query = "SELECT h.id as history_id, h.post_id, MIN(d.pin_date) as first_pin_date
              FROM " . DB_NAME . ".wp_pin_history h
              INNER JOIN " . DB_NAME . ".wp_pin_details d ON h.id = d.history_id
              GROUP BY h.id, h.post_id
              HAVING first_pin_date <= '$reset_date'";

    $result = $db_connection->query($query);
    if ($result) {
        $db_connection->begin_transaction();
        try {
            while ($row = $result->fetch_assoc()) {
                $history_id = $row['history_id'];
                // Delete all pin details for this post
                $delete_stmt = $db_connection->prepare("DELETE FROM " . DB_NAME . ".wp_pin_details WHERE history_id = ?");
                $delete_stmt->bind_param("i", $history_id);
                $delete_stmt->execute();
                $delete_stmt->close();
            }
            $db_connection->commit();
        } catch (Exception $e) {
            $db_connection->rollback();
            error_log("Error resetting expired pins: " . $e->getMessage());
        }
        $result->free();
    }
}

// Load pin history data
function load_pin_history() {
    global $db_connection;

    // Reset expired pins before loading data
    reset_expired_pins();

    $pin_history = array();
    $query = "SELECT h.*, d.pin_link, d.pin_date
              FROM " . DB_NAME . ".wp_pin_history h
              LEFT JOIN " . DB_NAME . ".wp_pin_details d ON h.id = d.history_id
              ORDER BY h.post_id";
    $result = $db_connection->query($query);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $post_id = $row['post_id'];
            if (!isset($pin_history[$post_id])) {
                $pin_history[$post_id] = array(
                    'title'        => $row['title'],
                    'url'          => $row['url'],
                    'publish_date' => $row['publish_date'],
                    'pins'         => array()
                );
            }
            if ($row['pin_link']) {
                $pin_history[$post_id]['pins'][] = array(
                    'link' => $row['pin_link'],
                    'date' => $row['pin_date']
                );
            }
        }
        $result->free();
    }
    return $pin_history;
}

// Get pin history for a specific post
function get_post_pin_history($post_id) {
    global $db_connection;
    $post_id = intval($post_id);
    $history = array(
        'post_id' => $post_id,
        'title' => get_the_title($post_id),
        'url' => get_permalink($post_id),
        'publish_date' => get_the_date('Y-m-d', $post_id),
        'pins' => array()
    );

    $query = "SELECT h.id as history_id, d.id as detail_id, d.pin_link, d.pin_date
              FROM " . DB_NAME . ".wp_pin_history h
              LEFT JOIN " . DB_NAME . ".wp_pin_details d ON h.id = d.history_id
              WHERE h.post_id = $post_id
              ORDER BY d.id";
    $result = $db_connection->query($query);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            if ($row['pin_link']) {
                $history['pins'][] = array(
                    'id' => $row['detail_id'],
                    'link' => $row['pin_link'],
                    'date' => $row['pin_date']
                );
            }
            $history['history_id'] = $row['history_id'];
        }
        $result->free();
    }
    return $history;
}

// Update pin history for a specific post
function update_pin_history($post_id, $pin_data) {
    global $db_connection;
    $post_id = intval($post_id);
    $title = get_the_title($post_id);
    $url = get_permalink($post_id);
    $publish_date = get_the_date('Y-m-d', $post_id);

    $db_connection->begin_transaction();
    try {
        // Insert or update the history record
        $stmt = $db_connection->prepare("INSERT INTO " . DB_NAME . ".wp_pin_history
        (post_id, title, url, publish_date) VALUES (?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE title = VALUES(title), url = VALUES(url), publish_date = VALUES(publish_date)");
        $stmt->bind_param("isss", $post_id, $title, $url, $publish_date);
        $stmt->execute();
        if (!$stmt->insert_id) {
            $history_id_result = $db_connection->query("SELECT id FROM " . DB_NAME . ".wp_pin_history WHERE post_id = " . $post_id);
            $history_row = $history_id_result->fetch_assoc();
            $history_id = $history_row['id'];
        } else {
            $history_id = $stmt->insert_id;
        }
        $stmt->close();

        // Delete existing pin details
        $stmt = $db_connection->prepare("DELETE FROM " . DB_NAME . ".wp_pin_details WHERE history_id = ?");
        $stmt->bind_param("i", $history_id);
        $stmt->execute();
        $stmt->close();

        // Insert new pin details
        if (!empty($pin_data)) {
            $stmt = $db_connection->prepare("INSERT INTO " . DB_NAME . ".wp_pin_details
                (history_id, pin_link, pin_date) VALUES (?, ?, ?)");
            foreach ($pin_data as $pin) {
                $stmt->bind_param("iss", $history_id, $pin['link'], $pin['date']);
                $stmt->execute();
            }
            $stmt->close();
        }

        $db_connection->commit();
        return true;
    } catch (Exception $e) {
        $db_connection->rollback();
        error_log("Error updating pin history: " . $e->getMessage());
        return false;
    }
}

// Clear pin history for multiple posts
function clear_pin_history($post_ids) {
    global $db_connection;
    $success = true;
    $db_connection->begin_transaction();
    try {
        foreach ($post_ids as $post_id) {
            $post_id = intval($post_id);
            $history_id_result = $db_connection->query("SELECT id FROM " . DB_NAME . ".wp_pin_history WHERE post_id = " . $post_id);
            if ($history_id_result && $history_row = $history_id_result->fetch_assoc()) {
                $history_id = $history_row['id'];
                $stmt = $db_connection->prepare("DELETE FROM " . DB_NAME . ".wp_pin_details WHERE history_id = ?");
                $stmt->bind_param("i", $history_id);
                $stmt->execute();
                $stmt->close();
            }
        }
        $db_connection->commit();
    } catch (Exception $e) {
        $db_connection->rollback();
        error_log("Error clearing pin history: " . $e->getMessage());
        $success = false;
    }
    return $success;
}

// Load pending events
function load_pending() {
    global $db_connection;
    $pending = array();
    $query = "SELECT * FROM " . DB_NAME . ".wp_pending_pins";
    $result = $db_connection->query($query);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $pending[] = array(
                'post_id'        => $row['post_id'],
                'event_type'     => $row['event_type'],
                'scheduled_date' => $row['scheduled_date'],
                'retry_count'    => $row['retry_count']
            );
        }
        $result->free();
    }
    return $pending;
}

// Load ignored posts
function load_ignored_posts() {
    global $db_connection;
    $ignored = array();
    $query = "SELECT post_id FROM " . DB_NAME . ".wp_ignored_posts";
    $result = $db_connection->query($query);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $ignored[] = $row['post_id'];
        }
        $result->free();
    }
    return $ignored;
}

// Get count of ignored posts
function get_ignored_posts_count() {
    global $db_connection;
    $query = "SELECT COUNT(*) as count FROM " . DB_NAME . ".wp_ignored_posts";
    $result = $db_connection->query($query);
    if ($result) {
        $row = $result->fetch_assoc();
        $result->free();
        return intval($row['count']);
    }
    return 0;
}

// Get ignored posts with details
function get_ignored_posts_with_details() {
    global $db_connection;
    $ignored_posts = array();
    $query = "SELECT ip.post_id, ip.ignored_date, p.post_title, p.post_date
              FROM " . DB_NAME . ".wp_ignored_posts ip
              JOIN " . DB_NAME . ".wp_posts p ON ip.post_id = p.ID
              WHERE p.post_status = 'publish'
              ORDER BY ip.ignored_date DESC";
    $result = $db_connection->query($query);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $ignored_posts[] = array(
                'post_id' => $row['post_id'],
                'title' => $row['post_title'],
                'ignored_date' => $row['ignored_date'],
                'publish_date' => $row['post_date']
            );
        }
        $result->free();
    }
    return $ignored_posts;
}

// Restore individual ignored post
function restore_ignored_post($post_id) {
    global $db_connection;
    $stmt = $db_connection->prepare("DELETE FROM " . DB_NAME . ".wp_ignored_posts WHERE post_id = ?");
    $stmt->bind_param("i", $post_id);
    $success = $stmt->execute();
    $stmt->close();
    return $success;
}

// Restore all ignored posts
function restore_all_ignored_posts() {
    global $db_connection;
    $query = "DELETE FROM " . DB_NAME . ".wp_ignored_posts";
    return $db_connection->query($query);
}

// Restore selected ignored posts
function restore_selected_ignored_posts($post_ids) {
    global $db_connection;
    if (empty($post_ids) || !is_array($post_ids)) {
        return false;
    }

    $placeholders = str_repeat('?,', count($post_ids) - 1) . '?';
    $query = "DELETE FROM " . DB_NAME . ".wp_ignored_posts WHERE post_id IN ($placeholders)";
    $stmt = $db_connection->prepare($query);

    if (!$stmt) {
        return false;
    }

    $types = str_repeat('i', count($post_ids));
    $stmt->bind_param($types, ...$post_ids);
    $success = $stmt->execute();
    $stmt->close();

    return $success;
}

// Save pin history data
function save_pin_history($data) {
    global $db_connection;
    $db_connection->begin_transaction();
    try {
        foreach ($data as $post_id => $post_data) {
            $stmt = $db_connection->prepare("INSERT INTO " . DB_NAME . ".wp_pin_history
            (post_id, title, url, publish_date) VALUES (?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE title = VALUES(title), url = VALUES(url), publish_date = VALUES(publish_date)");
            $stmt->bind_param("isss", $post_id, $post_data['title'], $post_data['url'], $post_data['publish_date']);
            $stmt->execute();
            if (!$stmt->insert_id) {
                $history_id_result = $db_connection->query("SELECT id FROM " . DB_NAME . ".wp_pin_history WHERE post_id = " . intval($post_id));
                $history_row = $history_id_result->fetch_assoc();
                $history_id = $history_row['id'];
            } else {
                $history_id = $stmt->insert_id;
            }
            $stmt->close();
            $stmt = $db_connection->prepare("DELETE FROM " . DB_NAME . ".wp_pin_details WHERE history_id = ?");
            $stmt->bind_param("i", $history_id);
            $stmt->execute();
            $stmt->close();
            if (!empty($post_data['pins'])) {
                $stmt = $db_connection->prepare("INSERT INTO " . DB_NAME . ".wp_pin_details
                    (history_id, pin_link, pin_date) VALUES (?, ?, ?)");
                foreach ($post_data['pins'] as $pin) {
                    $stmt->bind_param("iss", $history_id, $pin['link'], $pin['date']);
                    $stmt->execute();
                }
                $stmt->close();
            }
        }
        $db_connection->commit();
    } catch (Exception $e) {
        $db_connection->rollback();
        error_log("Error saving pin history: " . $e->getMessage());
    }
}

// Save a single pin
function save_single_pin($post_id, $pin_link) {
    global $db_connection;
    $title = get_the_title($post_id);
    $url = get_permalink($post_id);
    $publish_date = get_the_date('Y-m-d', $post_id);
    $today = date('Y-m-d');
    $db_connection->begin_transaction();
    try {
        $stmt = $db_connection->prepare("INSERT INTO " . DB_NAME . ".wp_pin_history
        (post_id, title, url, publish_date) VALUES (?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE title = VALUES(title), url = VALUES(url), publish_date = VALUES(publish_date)");
        $stmt->bind_param("isss", $post_id, $title, $url, $publish_date);
        $stmt->execute();
        if (!$stmt->insert_id) {
            $history_id_result = $db_connection->query("SELECT id FROM " . DB_NAME . ".wp_pin_history WHERE post_id = " . intval($post_id));
            $history_row = $history_id_result->fetch_assoc();
            $history_id = $history_row['id'];
        } else {
            $history_id = $stmt->insert_id;
        }
        $stmt->close();
        $stmt = $db_connection->prepare("INSERT INTO " . DB_NAME . ".wp_pin_details
            (history_id, pin_link, pin_date) VALUES (?, ?, ?)");
        $stmt->bind_param("iss", $history_id, $pin_link, $today);
        $stmt->execute();
        $stmt->close();
        $db_connection->commit();
        return true;
    } catch (Exception $e) {
        $db_connection->rollback();
        error_log("Error saving pin: " . $e->getMessage());
        return false;
    }
}

// Load submission history for the "Submission History" tab
function load_submission_history() {
    global $db_connection;
    $history = array();
    $query = "SELECT h.post_id, h.title, h.url, d.pin_link, d.pin_date
              FROM " . DB_NAME . ".wp_pin_history h
              INNER JOIN " . DB_NAME . ".wp_pin_details d ON h.id = d.history_id
              ORDER BY d.pin_date DESC";
    $result = $db_connection->query($query);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $history[] = $row;
        }
        $result->free();
    }
    return $history;
}

// Get new pin candidates (only listicle posts and not ignored)
function get_new_pin_candidates($posts, $pin_history, $today_str, $ignored_posts) {
    $new_pin_candidates = array();
    foreach ($posts as $post) {
        $post_id = $post->ID;
        if (in_array($post_id, $ignored_posts)) continue;
        $title = get_the_title($post_id);
        if (!preg_match('/\d+/', $title) && stripos($title, 'list') === false) {
            continue;
        }
        if (isset($pin_history[$post_id]) && !empty($pin_history[$post_id]['pins'])) {
            continue;
        }
        $url = get_permalink($post_id);
        $publish_date = get_the_date('Y-m-d', $post_id);
        $publish_time = strtotime($publish_date);
        $today_time   = strtotime($today_str);
        $days_old     = floor(($today_time - $publish_time) / (60 * 60 * 24));
        $new_pin_candidates[] = array(
            'id'           => $post_id,
            'title'        => $title,
            'url'          => $url,
            'publish_date' => $publish_date,
            'days_old'     => $days_old,
            'event_type'   => 'new_pin',
            'pins'         => array(),
            'total_pins'   => 5,
            'current_pin'  => 1
        );
    }
    return $new_pin_candidates;
}

// Prioritize new pin candidates
function prioritize_new_pin_candidates($candidates) {
    usort($candidates, function($a, $b) {
        if ($a['days_old'] === 0 && $b['days_old'] !== 0) return -1;
        if ($b['days_old'] === 0 && $a['days_old'] !== 0) return 1;
        $a_days = $a['days_old'];
        $b_days = $b['days_old'];
        if ($a_days <= 7 && $b_days > 7) return -1;
        if ($b_days <= 7 && $a_days > 7) return 1;
        if ($a_days <= 30 && $b_days > 30) return -1;
        if ($b_days <= 30 && $a_days > 30) return 1;
        return $a_days - $b_days;
    });
    return $candidates;
}

// Get repin candidates (only listicle posts and not ignored)
function get_repin_candidates($posts, $pin_history, $today_str, $ignored_posts) {
    $repin_candidates = array();
    foreach ($posts as $post) {
        $post_id = $post->ID;
        if (in_array($post_id, $ignored_posts)) continue;
        $title = get_the_title($post_id);
        if (!preg_match('/\d+/', $title) && stripos($title, 'list') === false) {
            continue;
        }
        $url = get_permalink($post_id);
        if (isset($pin_history[$post_id])) {
            $post_data = $pin_history[$post_id];
            $pins      = $post_data['pins'];
            $pin_count = count($pins);
            if ($pin_count > 0 && $pin_count < 5) {
                $last_pin = end($pins);
                $last_pin_date = strtotime($last_pin['date']);
                $days_since_last_pin = (strtotime($today_str) - $last_pin_date) / (60 * 60 * 24);
                if ($days_since_last_pin >= 7) {
                    $repin_candidates[] = array(
                        'id'             => $post_id,
                        'title'          => $title,
                        'url'            => $url,
                        'event_type'     => 'repin',
                        'scheduled_date' => $last_pin['date'],
                        'pin_count'      => $pin_count,
                        'days_since_last'=> floor($days_since_last_pin),
                        'retry_count'    => 0,
                        'pins'           => $pins,
                        'total_pins'     => 5,
                        'current_pin'    => $pin_count + 1
                    );
                }
            }
        }
    }
    usort($repin_candidates, function($a, $b) {
        $days_diff = $b['days_since_last'] - $a['days_since_last'];
        if ($days_diff !== 0) return $days_diff;
        return $a['pin_count'] - $b['pin_count'];
    });
    return $repin_candidates;
}

// CSV Import Helper Functions
function detect_csv_format($header) {
    // Check for normalized format (export format): Post Title, Pin Date, Pin Link
    $has_post_title = in_array('Post Title', $header);
    $has_pin_date = in_array('Pin Date', $header);
    $has_pin_link = in_array('Pin Link', $header);

    if ($has_post_title && $has_pin_date && $has_pin_link) {
        return 'normalized';
    }

    // Default to denormalized format (existing import format)
    return 'denormalized';
}

function process_normalized_csv($csv, $header) {
    $imported = 0;
    $skipped = 0;
    $pin_history = array();

    // Group pins by post title
    $grouped_pins = array();
    foreach ($csv as $row) {
        if (count($row) < count($header)) continue;

        $data = array_combine($header, $row);
        $title = isset($data['Post Title']) ? trim($data['Post Title']) : '';
        $pin_date = isset($data['Pin Date']) ? trim($data['Pin Date']) : '';
        $pin_link = isset($data['Pin Link']) ? trim($data['Pin Link']) : '';

        // Skip invalid rows
        if (empty($title) || empty($pin_date) || empty($pin_link)) {
            $skipped++;
            continue;
        }

        // Validate pin link
        if (strpos($pin_link, 'pinterest.com/pin/') === false) {
            $skipped++;
            continue;
        }

        // Validate date
        if (!strtotime($pin_date)) {
            $skipped++;
            continue;
        }

        $grouped_pins[$title][] = array(
            'link' => $pin_link,
            'date' => $pin_date
        );
    }

    // Process each post group
    foreach ($grouped_pins as $title => $pins) {
        // Find WordPress post by title
        $post = get_page_by_title($title, OBJECT, 'post');
        if (!$post) {
            $skipped += count($pins);
            continue;
        }

        // Sort pins by date (determines pin order: 1st, 2nd, 3rd...)
        usort($pins, function($a, $b) {
            return strtotime($a['date']) - strtotime($b['date']);
        });

        // Build pin history for this post
        $pin_history[$post->ID] = array(
            'title' => get_the_title($post->ID),
            'url' => get_permalink($post->ID),
            'publish_date' => get_the_date('Y-m-d', $post->ID),
            'pins' => $pins
        );

        $imported++;
    }

    return array(
        'success' => true,
        'pin_history' => $pin_history,
        'imported' => $imported,
        'skipped' => $skipped,
        'message' => "Successfully imported {$imported} posts with pin history. Skipped {$skipped} invalid entries."
    );
}

function process_denormalized_csv($csv, $header) {
    // This will contain the existing denormalized CSV processing logic
    // We'll move the existing logic here in the next step
    $imported = 0;
    $pin_history = array();

    foreach ($csv as $row) {
        // Skip rows that don't have enough columns
        if (count($row) < count($header)) continue;

        $data = array_combine($header, $row);

        // Get title and URL
        if (!isset($data['Title']) || !isset($data['URL'])) continue;
        $title = $data['Title'];
        $url = $data['URL'];

        // Handle both publish date column names
        $publish_date = '';
        if (isset($data['Publish Date'])) {
            $publish_date = $data['Publish Date'];
        } elseif (isset($data['Published Date'])) {
            $publish_date = $data['Published Date'];
        }

        if (empty($publish_date)) continue;

        $post_id = url_to_postid($url);
        if (!$post_id) continue;

        $pin_history[$post_id] = array(
            'title' => $title,
            'url' => $url,
            'publish_date' => $publish_date,
            'pins' => array()
        );

        for ($i = 1; $i <= 5; $i++) {
            // Check both pin link column naming formats
            $pin_link = null;
            $pin_date = null;

            // Check for Pin1 Link format
            $pin_link_col1 = "Pin{$i} Link";
            if (isset($data[$pin_link_col1]) && !empty($data[$pin_link_col1])) {
                $pin_link = $data[$pin_link_col1];
            }

            // Check for Pin-1 Link format
            $pin_link_col2 = "Pin-{$i} Link";
            if (isset($data[$pin_link_col2]) && !empty($data[$pin_link_col2])) {
                $pin_link = $data[$pin_link_col2];
            }

            // Check for Pin1 Date format
            $pin_date_col1 = "Pin{$i} Date";
            if (isset($data[$pin_date_col1]) && !empty($data[$pin_date_col1])) {
                $pin_date = $data[$pin_date_col1];
            }

            // Check for Pin-1 Publish Date format
            $pin_date_col2 = "Pin-{$i} Publish Date";
            if (isset($data[$pin_date_col2]) && !empty($data[$pin_date_col2])) {
                $pin_date = $data[$pin_date_col2];
            }

            if ($pin_link && $pin_date) {
                $pin_history[$post_id]['pins'][] = array(
                    'link' => $pin_link,
                    'date' => $pin_date
                );
            }
        }
        $imported++;
    }

    return array(
        'success' => true,
        'pin_history' => $pin_history,
        'imported' => $imported,
        'skipped' => 0,
        'message' => "Successfully imported {$imported} posts."
    );
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $pin_history = load_pin_history();
    $form_type   = isset($_POST['form_type']) ? $_POST['form_type'] : '';
    switch ($form_type) {
        case 'manage_pin_history':
            if (isset($_POST['post_id'])) {
                $post_id = intval($_POST['post_id']);

                // Check if this is a GET request for pin history data
                if (isset($_POST['action']) && $_POST['action'] === 'get') {
                    $history = get_post_pin_history($post_id);
                    $response = array(
                        'success' => true,
                        'data' => $history
                    );

                    header('Content-Type: application/json');
                    echo json_encode($response);
                    exit;
                }

                // Otherwise, this is an update request
                if (isset($_POST['pins'])) {
                    $pins = json_decode(stripslashes($_POST['pins']), true);

                    if ($post_id && is_array($pins)) {
                        $success = update_pin_history($post_id, $pins);
                        $response = array(
                            'success' => $success,
                            'message' => $success ? 'Pin history updated successfully!' : 'Error updating pin history.'
                        );
                    } else {
                        $response = array(
                            'success' => false,
                            'message' => 'Invalid data provided.'
                        );
                    }

                    header('Content-Type: application/json');
                    echo json_encode($response);
                    exit;
                }
            }
            break;
        case 'clear_pin_history':
            if (isset($_POST['post_ids']) && is_array($_POST['post_ids'])) {
                $post_ids = array_map('intval', $_POST['post_ids']);
                if (!empty($post_ids)) {
                    $success = clear_pin_history($post_ids);
                    $response = array(
                        'success' => $success,
                        'message' => $success ? 'Pin history cleared successfully!' : 'Error clearing pin history.'
                    );
                } else {
                    $response = array(
                        'success' => false,
                        'message' => 'No posts selected.'
                    );
                }

                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
                    header('Content-Type: application/json');
                    echo json_encode($response);
                    exit;
                } else {
                    $_SESSION['message'] = array(
                        'type' => $response['success'] ? 'success' : 'danger',
                        'text' => $response['message']
                    );
                }
            }
            break;
        case 'add_pin':
            if (isset($_POST['post_id']) && isset($_POST['pin_link'])) {
                $post_id = intval($_POST['post_id']);
                $pin_link = trim($_POST['pin_link']);
                if ($post_id && !empty($pin_link)) {
                    $success = save_single_pin($post_id, $pin_link);
                    $_SESSION['message'] = array(
                        'type' => $success ? 'success' : 'danger',
                        'text' => $success ? "Pin added successfully!" : "Error adding pin. Please try again."
                    );
                    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
                        header('Content-Type: application/json');
                        echo json_encode(array(
                            'success' => $success,
                            'message' => $_SESSION['message']['text']
                        ));
                        exit;
                    }
                }
            }
            break;
        case 'bulk_table':
            if (isset($_POST['update_status']) && is_array($_POST['update_status'])) {
                foreach ($_POST['update_status'] as $post_id => $status) {
                    $post_id = intval($post_id);
                    $status = intval($status);
                    if (!isset($_POST['bulk_pin_link'][$post_id]) || !isset($_POST['bulk_pin_date'][$post_id])) {
                        continue;
                    }
                    $provided_url = trim($_POST['bulk_pin_link'][$post_id]);
                    $provided_date = trim($_POST['bulk_pin_date'][$post_id]);
                    if (strpos($provided_url, "https://www.pinterest.com/pin/") !== 0) {
                        continue;
                    }
                    if (!$provided_date || !strtotime($provided_date)) {
                        continue;
                    }
                    if (!isset($pin_history[$post_id])) {
                        $pin_history[$post_id] = array(
                            'title'        => get_the_title($post_id),
                            'url'          => get_permalink($post_id),
                            'publish_date' => get_the_date('Y-m-d', $post_id),
                            'pins'         => array()
                        );
                    }
                    $current_pins = isset($pin_history[$post_id]['pins']) ? count($pin_history[$post_id]['pins']) : 0;
                    if ($status > $current_pins) {
                        for ($i = $current_pins; $i < $status; $i++) {
                            $pin_history[$post_id]['pins'][] = array(
                                'link' => $provided_url,
                                'date' => $provided_date
                            );
                        }
                    } else if ($status < $current_pins) {
                        $pin_history[$post_id]['pins'] = array_slice($pin_history[$post_id]['pins'], 0, $status);
                    }
                }
                save_pin_history($pin_history);
                $_SESSION['message'] = array(
                    'type' => 'success',
                    'text' => 'Bulk updates completed successfully!'
                );
            }
            break;
        case 'bulk_individual_update':
            // Add debug logging
            error_log("Received bulk_individual_update request: " . json_encode($_POST));

            $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
            $status = isset($_POST['status']) ? intval($_POST['status']) : 0;
            $provided_url = isset($_POST['bulk_pin_link']) ? trim($_POST['bulk_pin_link']) : '';
            $provided_date = isset($_POST['bulk_pin_date']) ? trim($_POST['bulk_pin_date']) : '';

            error_log("Parsed values - post_id: $post_id, status: $status, url: $provided_url, date: $provided_date");

            if ($post_id && $provided_url && $provided_date) {
                if (strpos($provided_url, "https://www.pinterest.com/pin/") !== 0 || !$provided_date || !strtotime($provided_date)) {
                    $response = array(
                        'success' => false,
                        'message' => 'Invalid URL or date.'
                    );
                    error_log("Invalid URL or date: $provided_url, $provided_date");
                } else {
                    if (!isset($pin_history[$post_id])) {
                        $pin_history[$post_id] = array(
                            'title'        => get_the_title($post_id),
                            'url'          => get_permalink($post_id),
                            'publish_date' => get_the_date('Y-m-d', $post_id),
                            'pins'         => array()
                        );
                        error_log("Created new pin history entry for post $post_id");
                    }

                    $current_pins = isset($pin_history[$post_id]['pins']) ? count($pin_history[$post_id]['pins']) : 0;
                    error_log("Current pins for post $post_id: $current_pins, new status: $status");

                    // If we're updating an existing pin (not adding a new one)
                    if ($status > 0 && $status <= $current_pins) {
                        // Update the specific pin at the status position
                        $pin_history[$post_id]['pins'][$status - 1] = array(
                            'link' => $provided_url,
                            'date' => $provided_date
                        );
                        error_log("Updated pin at position $status with new URL and date");
                    } else if ($status > $current_pins) {
                        // Add new pins to reach the desired status
                        for ($i = $current_pins; $i < $status; $i++) {
                            $pin_history[$post_id]['pins'][] = array(
                                'link' => $provided_url,
                                'date' => $provided_date
                            );
                        }
                        error_log("Added pins to reach status $status");
                    } else if ($status < $current_pins) {
                        // Reduce pins to match the desired status
                        $pin_history[$post_id]['pins'] = array_slice($pin_history[$post_id]['pins'], 0, $status);
                        error_log("Reduced pins to status $status");
                    }

                    try {
                        save_pin_history($pin_history);
                        error_log("Successfully saved pin history");
                        $response = array(
                            'success' => true,
                            'message' => 'Post updated successfully!'
                        );
                    } catch (Exception $e) {
                        error_log("Error saving pin history: " . $e->getMessage());
                        $response = array(
                            'success' => false,
                            'message' => 'Error saving pin history: ' . $e->getMessage()
                        );
                    }
                }
            } else {
                $response = array(
                    'success' => false,
                    'message' => 'Missing required data for update.'
                );
                error_log("Missing required data for update - post_id: $post_id, url: $provided_url, date: $provided_date");
            }

            error_log("Sending response: " . json_encode($response));
            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
            break;
        case 'pin_link_bulk':
            if (isset($_POST['bulk_pin_link']) && is_array($_POST['bulk_pin_link'])) {
                foreach ($_POST['bulk_pin_link'] as $post_id => $pin_link) {
                    $post_id = intval($post_id);
                    $pin_link = trim($pin_link);
                    if ($post_id && !empty($pin_link)) {
                        save_single_pin($post_id, $pin_link);
                    }
                }
                $_SESSION['message'] = array(
                    'type' => 'success',
                    'text' => 'Pin links updated successfully!'
                );
            }
            break;
        case 'csv_import':
            if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] == 0) {
                $file = $_FILES['csv_file']['tmp_name'];
                $csv = array_map('str_getcsv', file($file));
                $header = array_shift($csv);

                // Auto-detect CSV format
                $format = detect_csv_format($header);

                if ($format === 'normalized') {
                    // Handle normalized format (Post Title, Pin Date, Pin Link)
                    $result = process_normalized_csv($csv, $header);
                } else {
                    // Handle denormalized format (existing format)
                    // Check for required columns with flexible naming
                    $has_title = in_array('Title', $header);
                    $has_url = in_array('URL', $header);
                    $has_publish_date = in_array('Publish Date', $header) || in_array('Published Date', $header);

                    if (!$has_title || !$has_url || !$has_publish_date) {
                        $_SESSION['message'] = array(
                            'type' => 'danger',
                            'text' => 'Invalid CSV format. Required columns missing: Title, URL, and Publish Date/Published Date.'
                        );
                        break;
                    }

                    $result = process_denormalized_csv($csv, $header);
                }

                // Process results
                if ($result['success'] && $result['imported'] > 0) {
                    save_pin_history($result['pin_history']);
                    $_SESSION['message'] = array(
                        'type' => 'success',
                        'text' => $result['message']
                    );
                } else if ($result['success'] && $result['imported'] == 0) {
                    $_SESSION['message'] = array(
                        'type' => 'warning',
                        'text' => "No valid posts found in the CSV file. Please check the format and content."
                    );
                } else {
                    $_SESSION['message'] = array(
                        'type' => 'danger',
                        'text' => $result['message'] ?? 'Error processing CSV file.'
                    );
                }
            } else {
                $_SESSION['message'] = array(
                    'type' => 'danger',
                    'text' => 'Error uploading file. Please try again.'
                );
            }
            break;
        case 'ignore_post':
            if (isset($_POST['post_id'])) {
                $post_id = intval($_POST['post_id']);
                $today = date('Y-m-d');
                $stmt = $db_connection->prepare("INSERT IGNORE INTO " . DB_NAME . ".wp_ignored_posts (post_id, ignored_date) VALUES (?, ?)");
                $stmt->bind_param("is", $post_id, $today);
                $stmt->execute();
                $stmt->close();
                $_SESSION['message'] = array(
                    'type' => 'success',
                    'text' => 'Post ignored successfully!'
                );
            }
            break;
        case 'restore_ignored_post':
            if (isset($_POST['post_id'])) {
                $post_id = intval($_POST['post_id']);
                if (restore_ignored_post($post_id)) {
                    $_SESSION['message'] = array(
                        'type' => 'success',
                        'text' => 'Post restored successfully!'
                    );
                } else {
                    $_SESSION['message'] = array(
                        'type' => 'danger',
                        'text' => 'Error restoring post. Please try again.'
                    );
                }
            }
            break;
        case 'restore_all_ignored_posts':
            if (restore_all_ignored_posts()) {
                $_SESSION['message'] = array(
                    'type' => 'success',
                    'text' => 'All ignored posts restored successfully!'
                );
            } else {
                $_SESSION['message'] = array(
                    'type' => 'danger',
                    'text' => 'Error restoring posts. Please try again.'
                );
            }
            break;
        case 'bulk_restore_ignored_posts':
            if (isset($_POST['post_ids']) && is_array($_POST['post_ids'])) {
                $post_ids = array_map('intval', $_POST['post_ids']);
                $post_ids = array_filter($post_ids, function($id) { return $id > 0; });

                if (!empty($post_ids)) {
                    if (restore_selected_ignored_posts($post_ids)) {
                        $count = count($post_ids);
                        $_SESSION['message'] = array(
                            'type' => 'success',
                            'text' => "Successfully restored $count ignored post(s)!"
                        );
                    } else {
                        $_SESSION['message'] = array(
                            'type' => 'danger',
                            'text' => 'Error restoring selected posts. Please try again.'
                        );
                    }
                } else {
                    $_SESSION['message'] = array(
                        'type' => 'warning',
                        'text' => 'No valid posts selected for restoration.'
                    );
                }
            } else {
                $_SESSION['message'] = array(
                    'type' => 'warning',
                    'text' => 'No posts selected for restoration.'
                );
            }
            break;
        case 'send_email_report':
            // Handle AJAX email sending request
            $result = send_pin_report_email();

            header('Content-Type: application/json');
            echo json_encode($result);
            exit;
            break;
    }
    if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
        header("Location: " . $_SERVER['REQUEST_URI']);
        exit;
    }
}

// Get message from session if it exists
$message = isset($_SESSION['message']) ? $_SESSION['message'] : '';
unset($_SESSION['message']);

$today_str = current_time('Y-m-d');
$today     = new DateTime($today_str, new DateTimeZone('Asia/Dhaka'));

// Query posts
$args = array(
    'post_status'    => 'publish',
    'posts_per_page' => -1,
    'orderby'        => 'date',
    'order'          => 'DESC',
    'cache_results'  => true
);
$query = new WP_Query($args);
$posts = $query->posts;

// Load data
$pin_history = load_pin_history();
$ignored_posts = load_ignored_posts();
$new_pin_candidates = get_new_pin_candidates($posts, $pin_history, $today_str, $ignored_posts);
$prioritized_candidates = prioritize_new_pin_candidates($new_pin_candidates);
$repin_candidates = get_repin_candidates($posts, $pin_history, $today_str, $ignored_posts);

$items_per_page = 10;

// Calculate account age and initial daily quota based on account age
$oldest_date_query = "SELECT MIN(publish_date) as oldest_date FROM " . DB_NAME . ".wp_pin_history";
$result = $db_connection->query($oldest_date_query);
$row = $result->fetch_assoc();
$oldest_date = strtotime($row['oldest_date']);
$days = 0;
if ($oldest_date) {
    $first_pin_date = new DateTime();
    $first_pin_date->setTimestamp($oldest_date);
    $interval = $first_pin_date->diff($today);
    $days = $interval->days;
}
$initial_quota = 10;
if ($days >= 150) {
    $initial_quota = 50;
} elseif ($days >= 90) {
    $initial_quota = 35;
} elseif ($days >= 30) {
    $initial_quota = 20;
}

// Get number of pins submitted today
$submitted_today = get_today_pin_count();
// Calculate remaining quota
$daily_quota = $initial_quota - $submitted_today;
if ($daily_quota < 0) {
    $daily_quota = 0;
}

$max_new_pins = min(ceil($daily_quota * 0.4), count($prioritized_candidates));
$max_repins   = min($daily_quota - $max_new_pins, count($repin_candidates));

$new_pins    = array_slice($prioritized_candidates, 0, $max_new_pins);
$repin_tasks = array_slice($repin_candidates, 0, $max_repins);

// Load submission history for the new tab
$submission_history = load_submission_history();

// Calculate statistics for dashboard
$status_counts = get_posts_by_status($posts, $pin_history, $ignored_posts);
$total_posts = get_total_posts_count($posts, $ignored_posts);
$completion_percentage = get_completion_percentage($status_counts);
$progress_percentage = get_progress_percentage($status_counts);
$posts_with_progress = get_posts_with_progress($status_counts);
$weekly_pins = get_weekly_pin_stats();
$monthly_pins = get_monthly_pin_stats();
$pending_posts = get_pending_posts_count($status_counts);
$ignored_posts_count = get_ignored_posts_count();
$ignored_posts_details = get_ignored_posts_with_details();

// Get today's pin count before closing database connection
$today_pin_count = get_today_pin_count();

// Close DB connection
$db_connection->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Enhanced Pin Dashboard - <?php echo $today_str; ?></title>

    <!-- Font and Framework CDNs -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- Updated Color Scheme with Soft Pastels -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">

    <style>
    :root {
        --primary-color: #8ab4f8;
        --primary-hover: #669df6;
        --secondary-color: #a1c4fd;
        --success-color: #c8e6c9;
        --info-color: #bbdefb;
        --warning-color: #ffe082;
        --danger-color: #ffab91;
        --light-color: #f8f9fa;
        --dark-color: #212529;
        --border-color: #dee2e6;
        --surface-color: #ffffff;
        --surface-hover: #f1f3f9;

        --shadow-sm: 0 .125rem .25rem rgba(0,0,0,.075);
        --shadow-md: 0 .25rem .75rem rgba(0,0,0,.1);
        --shadow-lg: 0 .5rem 1.5rem rgba(0,0,0,.125);

        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;

        --border-radius-sm: 0.25rem;
        --border-radius-md: 0.5rem;
        --border-radius-lg: 0.75rem;
        --border-radius-xl: 1rem;

        --transition-fast: 0.2s;
        --transition-normal: 0.3s;
        --transition-slow: 0.5s;
    }

    body {
        font-family: 'Roboto', sans-serif;
        background-color: #f5f7fa;
        color: #333;
        line-height: 1.5;
    }

    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: var(--spacing-xl) var(--spacing-md);
    }

    .pin-tabs {
        display: flex;
        margin-bottom: var(--spacing-lg);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
        background-color: var(--surface-color);
    }

    .pin-tab {
        flex: 1;
        padding: var(--spacing-md) var(--spacing-sm);
        text-align: center;
        font-weight: 500;
        color: #667080;
        background-color: var(--surface-color);
        border: none;
        cursor: pointer;
        transition: all var(--transition-normal);
        position: relative;
    }

    .pin-tab:hover {
        color: var(--primary-color);
        background-color: var(--surface-hover);
    }

    .pin-tab.active {
        color: var(--primary-color);
        background-color: var(--surface-color);
    }

    .pin-tab.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background-color: var(--primary-color);
        animation: slideIn 0.3s ease-out;
    }

    .pin-tab-indicator {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-left: var(--spacing-xs);
        background: var(--primary-color);
        color: white;
        font-size: 0.75rem;
        border-radius: 1rem;
        min-width: 1.5rem;
        height: 1.5rem;
        padding: 0 0.35rem;
    }

    .pin-tab-content {
        display: none;
        animation: fadeIn 0.3s ease;
    }

    .pin-tab-content.active {
        display: block;
    }

    /* Alert Container Styling */
    .alert-container {
        margin-bottom: 1rem;
        min-height: 0;
    }

    .alert-container:empty {
        margin-bottom: 0;
    }

    .card {
        border: none;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-md);
        background-color: var(--surface-color);
        margin-bottom: var(--spacing-lg);
        overflow: hidden;
        transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    }

    .card:hover {
        box-shadow: var(--shadow-lg);
    }

    .card-header {
        background-color: var(--surface-color);
        border-bottom: 1px solid var(--border-color);
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .card-header h3 {
        margin: 0;
        font-weight: 500;
    }

    .card-body {
        padding: var(--spacing-lg);
    }

    .task-item {
        padding: var(--spacing-md);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-md);
        margin-bottom: var(--spacing-md);
        background-color: var(--surface-color);
        transition: all var(--transition-normal);
        box-shadow: var(--shadow-sm);
        position: relative;
    }

    .task-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .task-item-title {
        font-size: 1rem;
        font-weight: 500;
        margin-bottom: var(--spacing-xs);
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .task-item-url {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: var(--spacing-sm);
        display: block;
        word-break: break-all;
    }

    .pin-progress {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        margin: var(--spacing-sm) 0;
    }

    .pin-indicator {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        font-weight: 500;
        color: #8e9aaf;
        background-color: #edf2fb;
        transition: all var(--transition-fast);
        cursor: pointer;
    }

    .pin-indicator.completed {
        background-color: var(--success-color);
        color: white;
    }

    .pin-indicator:hover {
        transform: scale(1.1);
        background-color: var(--primary-color);
        color: white;
    }

    .pin-indicator.completed:hover {
        background-color: var(--primary-hover);
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.3rem 0.6rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 500;
        margin-right: var(--spacing-xs);
        margin-bottom: var(--spacing-xs);
    }

    .status-badge i {
        margin-right: var(--spacing-xs);
    }

    .form-check-input {
        width: 1.15rem;
        height: 1.15rem;
        margin-top: 0.2rem;
        border-color: var(--border-color);
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .form-select {
        border-radius: var(--border-radius-md);
        border: 1px solid var(--border-color);
        padding: 0.5rem 2.25rem 0.5rem 0.75rem;
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .form-select:focus {
        box-shadow: 0 0 0 0.15rem rgba(67, 97, 238, 0.25);
    }

    .btn {
        border-radius: var(--border-radius-md);
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all var(--transition-fast);
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background-color: var(--primary-hover);
        border-color: var(--primary-hover);
    }

    .btn-submit {
        display: none;
        background-color: var(--success-color);
        border-color: var(--success-color);
        color: white;
        padding: 0.5rem 2rem;
        box-shadow: var(--shadow-sm);
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 999;
    }

    .btn-submit.show {
        display: inline-flex;
        animation: bounceIn 0.5s;
    }

    .btn-update {
        position: absolute;
        top: 10px;
        right: 10px;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter {
        margin-bottom: var(--spacing-md);
    }

    .dataTables_wrapper .dataTables_filter input {
        border-radius: var(--border-radius-md);
        border: 1px solid var(--border-color);
        padding: 0.5rem;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        border-radius: var(--border-radius-md);
        padding: 0.3rem 0.6rem;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white !important;
    }

    table.dataTable {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
    }

    table.dataTable thead th {
        padding: 1rem;
        font-weight: 500;
        color: #555;
    }

    table.dataTable tbody td {
        padding: 1rem;
        vertical-align: middle;
    }

    .daterangepicker {
        font-family: 'Roboto', sans-serif;
        border-radius: var(--border-radius-md);
        box-shadow: var(--shadow-md);
        border: none;
    }

    .daterangepicker .ranges li.active {
        background-color: var(--primary-color);
    }

    .daterangepicker td.active,
    .daterangepicker td.active:hover {
        background-color: var(--primary-color);
    }

    .daterangepicker .drp-buttons .btn {
        font-weight: 500;
    }

    .date-filter-container {
        position: relative;
        display: inline-block;
    }

    .date-filter-container .clear-date {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
        cursor: pointer;
        z-index: 5;
    }

    .date-filter-input {
        padding-right: 30px;
    }

    .items-count {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: var(--spacing-md);
    }

    .mini-pagination {
        display: flex;
        justify-content: center;
        margin-top: var(--spacing-md);
    }

    .mini-pagination .page-item {
        margin: 0 3px;
    }

    .mini-pagination .page-link {
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.85rem;
        color: #555;
    }

    .mini-pagination .page-item.active .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes slideIn {
        from { transform: scaleX(0); }
        to { transform: scaleX(1); }
    }

    @keyframes bounceIn {
        0% { transform: scale(0.8); opacity: 0; }
        50% { transform: scale(1.05); opacity: 1; }
        100% { transform: scale(1); opacity: 1; }
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .py-lg {
        padding-top: var(--spacing-lg);
        padding-bottom: var(--spacing-lg);
    }

    .opacity-75 {
        opacity: 0.75;
    }

    @media (max-width: 991.98px) {
        .pin-tab {
            padding: 0.75rem 0.5rem;
            font-size: 0.9rem;
        }

        .pin-tab .text {
            display: none;
        }

        .pin-tab i {
            margin-right: 0;
        }

        .pin-tab-indicator {
            margin-left: 0;
            margin-top: 5px;
        }

        .dashboard-container {
            padding: var(--spacing-md) var(--spacing-sm);
        }

        .card-header, .card-body {
            padding: var(--spacing-md);
        }
    }

    @media (max-width: 767.98px) {
        .btn-submit {
            width: calc(100% - 2rem);
            right: 1rem;
            bottom: 1rem;
        }

        .pin-tab i {
            font-size: 1.1rem;
        }

        table.dataTable thead th,
        table.dataTable tbody td {
            padding: 0.75rem 0.5rem;
        }
    }

    /* Statistics Dashboard Styles */
    .statistics-dashboard {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: var(--border-radius-lg);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }

    /* Clickable stat cards */
    .clickable-stat-card {
        transition: all 0.3s ease;
    }

    .clickable-stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .clickable-stat-card.active {
        border: 3px solid #fff;
        box-shadow: 0 0 0 2px var(--primary-color);
    }

    .stat-card {
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        transition: all var(--transition-normal);
        cursor: pointer;
        border: 1px solid rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .stat-icon {
        font-size: 1.5rem;
        opacity: 0.8;
    }

    .stat-content {
        flex: 1;
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.25rem;
    }

    .stat-label {
        font-size: 0.75rem;
        opacity: 0.9;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .summary-card {
        background: var(--surface-color);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        box-shadow: var(--shadow-sm);
        transition: all var(--transition-normal);
        border: 1px solid var(--border-color);
    }

    .summary-card:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    .summary-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
    }

    .summary-content {
        flex: 1;
    }

    .summary-number {
        font-size: 1.75rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.25rem;
        color: var(--dark-color);
    }

    .summary-label {
        font-size: 0.875rem;
        color: #6c757d;
        font-weight: 500;
    }

    .progress-section {
        background: var(--surface-color);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
        border: 1px solid var(--border-color);
    }

    .quick-action-card {
        background: var(--surface-color);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
        border: 1px solid var(--border-color);
        transition: all var(--transition-normal);
        cursor: pointer;
    }

    .quick-action-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
        border-color: var(--primary-color);
    }

    .quick-action-icon {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
    }

    .quick-action-title {
        font-weight: 500;
        font-size: 0.9rem;
        color: var(--dark-color);
        margin-bottom: 0.25rem;
    }

    .quick-action-subtitle {
        font-size: 0.75rem;
        color: #6c757d;
    }

    .analytics-chart {
        background: var(--surface-color);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
        border: 1px solid var(--border-color);
        margin-bottom: var(--spacing-md);
    }

    .chart-header {
        display: flex;
        justify-content-between;
        align-items-center;
        margin-bottom: var(--spacing-md);
    }

    .chart-title {
        font-weight: 500;
        color: var(--dark-color);
    }

    .chart-value {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    @media (max-width: 768px) {
        .statistics-dashboard {
            padding: var(--spacing-md);
        }

        .stat-number {
            font-size: 1.25rem;
        }

        .summary-number {
            font-size: 1.5rem;
        }

        .stat-card, .summary-card {
            padding: var(--spacing-sm);
        }

        .quick-action-card {
            padding: var(--spacing-sm);
        }

        .quick-action-icon {
            width: 2rem;
            height: 2rem;
            font-size: 0.875rem;
        }
    }

    /* Import Tab Styles */
    .file-upload-area {
        border: 2px dashed var(--border-color);
        border-radius: var(--border-radius-lg);
        background: var(--surface-color);
        transition: all var(--transition-normal);
        cursor: pointer;
    }

    .file-upload-area:hover {
        border-color: var(--primary-color);
        background: var(--surface-hover);
    }

    .file-upload-area.dragover {
        border-color: var(--primary-color);
        background: var(--primary-color);
        color: white;
    }

    .file-upload-content {
        padding: var(--spacing-xl);
        text-align: center;
    }

    .requirement-card {
        background: var(--surface-color);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
        height: 100%;
    }

    .step-card {
        background: var(--surface-color);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-md);
        padding: var(--spacing-md);
        text-align: center;
        transition: all var(--transition-normal);
        position: relative;
        margin-bottom: var(--spacing-md);
    }

    .step-card.active {
        border-color: var(--primary-color);
        background: var(--primary-color);
        color: white;
    }

    .step-card.completed {
        border-color: var(--success-color);
        background: var(--success-color);
        color: white;
    }

    .step-number {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        background: var(--border-color);
        color: var(--dark-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin: 0 auto var(--spacing-sm);
    }

    .step-card.active .step-number {
        background: white;
        color: var(--primary-color);
    }

    .step-card.completed .step-number {
        background: white;
        color: var(--success-color);
    }

    .step-title {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .step-description {
        font-size: 0.875rem;
        opacity: 0.8;
    }

    /* Bulk Update Form Styles */
    .bulk-action-form {
        min-width: 300px;
    }

    .bulk-action-form .input-group {
        margin-bottom: 0.5rem;
    }

    .bulk-action-form .d-flex {
        gap: 0.25rem;
    }

    .bulk-action-form .btn {
        white-space: nowrap;
    }

    /* Enhanced Badge Styles */
    .badge {
        font-size: 0.75rem;
        font-weight: 500;
        border-radius: 0.375rem;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 500;
        border-radius: 0.375rem;
        color: white;
    }

    .status-badge i {
        margin-right: 0.25rem;
    }

    /* Button hover effects for Bulk Update tab */
    .btn-update-individual:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4) !important;
    }

    .btn-manage-history:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(66, 153, 225, 0.4) !important;
    }

    .btn[title="Ignore this post"]:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(248, 181, 0, 0.3) !important;
    }

    /* Enhanced Input Field Styling */
    .form-control-enhanced {
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        padding: 10px 12px;
        font-size: 14px;
        transition: all 0.3s ease;
        background: #ffffff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        font-weight: 500;
    }

    .form-control-enhanced:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    }

    /* Date Input Specific Styling */
    .date-input-enhanced {
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        padding: 8px 12px;
        font-weight: 500;
        color: #2d3748;
        transition: all 0.3s ease;
    }

    .date-input-enhanced:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    /* URL Input Styling */
    .url-input-enhanced {
        background: linear-gradient(135deg, #f0fff4 0%, #ffffff 100%);
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        padding: 10px 12px;
        font-size: 14px;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .url-input-enhanced:focus {
        border-color: #48bb78;
        box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1);
        outline: none;
    }

    /* Select Dropdown Enhanced Styling */
    .select-enhanced {
        background: linear-gradient(135deg, #faf5ff 0%, #ffffff 100%);
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        padding: 8px 12px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .select-enhanced:focus {
        border-color: #805ad5;
        box-shadow: 0 0 0 3px rgba(128, 90, 213, 0.1);
        outline: none;
    }

    /* Button hover effects for New Pins and Re-Pins tabs */
    .update-single-pin:hover,
    .update-single-repin:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4) !important;
    }

    /* Export CSV Button Styling */
    .btn-export-csv {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
        border: none !important;
        color: white !important;
        border-radius: 6px;
        padding: 8px 16px;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(72, 187, 120, 0.3);
        transition: all 0.2s ease;
    }

    .btn-export-csv:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(72, 187, 120, 0.4) !important;
        color: white !important;
    }

    /* View Pin Button Styling */
    .btn-view-pin {
        background: linear-gradient(135deg, #805ad5 0%, #6b46c1 100%) !important;
        border: none !important;
        color: white !important;
        border-radius: 6px;
        padding: 6px 12px;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(128, 90, 213, 0.3);
        transition: all 0.2s ease;
        text-decoration: none !important;
    }

    .btn-view-pin:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(128, 90, 213, 0.4) !important;
        color: white !important;
        text-decoration: none !important;
    }



    /* Smooth transitions for all buttons */
    .btn {
        transition: all 0.2s ease;
    }

    /* Improved table styling */
    .table-striped > tbody > tr:nth-of-type(odd) > td {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table-striped > tbody > tr:hover > td {
        background-color: rgba(102, 126, 234, 0.1);
    }

    /* Professional Row Number Badge for Submission History */
    .row-number-badge {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        color: white;
        border: 2px solid #e5e7eb;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
        box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
        transition: all 0.2s ease;
    }

    .row-number-badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
        border-color: #c7d2fe;
    }

    /* Enhanced DataTable Controls Styling */
    .dataTables_wrapper .dataTables_length select {
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
        border: 2px solid #e1e8ed;
        border-radius: 8px;
        padding: 6px 12px;
        font-weight: 500;
        color: #2d3748;
        transition: all 0.3s ease;
        min-width: 80px;
    }

    .dataTables_wrapper .dataTables_length select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    .dataTables_wrapper .dataTables_filter input {
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
        border: 2px solid #e1e8ed !important;
        border-radius: 8px !important;
        padding: 8px 12px !important;
        font-weight: 500;
        color: #2d3748;
        transition: all 0.3s ease;
        min-width: 200px;
    }

    .dataTables_wrapper .dataTables_filter input:focus {
        border-color: #667eea !important;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
        outline: none !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
        border: 2px solid #e1e8ed !important;
        border-radius: 6px !important;
        padding: 6px 12px !important;
        margin: 0 2px;
        font-weight: 500;
        color: #2d3748 !important;
        transition: all 0.2s ease;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-color: #667eea !important;
        color: white !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-color: #667eea !important;
        color: white !important;
        box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    }

    .dataTables_wrapper .dataTables_length label,
    .dataTables_wrapper .dataTables_filter label {
        font-weight: 500;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    /* Enhanced Submission History Controls Styling */
    #history-filter-select {
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
        border: 2px solid #e1e8ed;
        border-radius: 12px !important;
        padding: 10px 16px;
        font-weight: 500;
        font-size: 14px;
        color: #2d3748;
        transition: all 0.3s ease;
        min-width: 140px;
        height: 42px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    #history-filter-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    #history-custom-range {
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%) !important;
        border: 2px solid #e1e8ed !important;
        border-radius: 12px !important;
        padding: 10px 16px !important;
        font-weight: 500 !important;
        font-size: 14px !important;
        color: #2d3748 !important;
        min-width: 240px;
        max-width: 300px;
        height: 42px !important;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        cursor: pointer;
    }

    #history-custom-range:focus {
        border-color: #667eea !important;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        outline: none !important;
        transform: translateY(-1px);
    }

    #history-custom-range:hover {
        border-color: #a0aec0 !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
    }

    #history-custom-range::placeholder {
        color: #a0aec0 !important;
        font-style: italic;
    }

    /* Enhanced Export CSV Button */
    .btn-export-csv {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%) !important;
        border: 2px solid #38a169 !important;
        color: white !important;
        border-radius: 12px !important;
        padding: 10px 16px !important;
        font-weight: 500 !important;
        font-size: 14px !important;
        height: 42px !important;
        min-width: 130px;
        max-width: 150px;
        box-shadow: 0 2px 4px rgba(72, 187, 120, 0.3);
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .btn-export-csv:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(72, 187, 120, 0.4) !important;
        color: white !important;
        border-color: #2f855a !important;
        background: linear-gradient(135deg, #38a169 0%, #2f855a 100%) !important;
    }

    .btn-export-csv:focus {
        box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.2), 0 4px 8px rgba(72, 187, 120, 0.3) !important;
        outline: none !important;
        color: white !important;
    }

    .btn-export-csv:active {
        transform: translateY(0px);
        box-shadow: 0 2px 4px rgba(72, 187, 120, 0.3) !important;
    }
    </style>

</head>
<body>

    <div class="dashboard-container">
        <header class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">Pin Dashboard</h1>
                <div class="d-flex align-items-center gap-3 flex-wrap">
                    <span class="badge" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                              color: white; padding: 8px 16px; border-radius: 20px;
                                              font-weight: 500; font-size: 0.875rem;
                                              box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);">
                        <i class="fas fa-calendar-day me-2"></i>
                        <?php echo $today_str; ?>
                    </span>
                    <span class="badge" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                                              color: white; padding: 8px 16px; border-radius: 20px;
                                              font-weight: 500; font-size: 0.875rem;
                                              box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);">
                        <i class="fas fa-thumbtack me-2"></i>
                        Submitted Today: <?php echo $submitted_today; ?>
                    </span>
                    <span class="badge" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                                              color: white; padding: 8px 16px; border-radius: 20px;
                                              font-weight: 500; font-size: 0.875rem;
                                              box-shadow: 0 2px 8px rgba(67, 233, 123, 0.3);">
                        <i class="fas fa-chart-line me-2"></i>
                        Remaining: <?php echo $daily_quota; ?>
                    </span>
                    <span class="badge" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
                                              color: white; padding: 8px 16px; border-radius: 20px;
                                              font-weight: 500; font-size: 0.875rem;
                                              box-shadow: 0 2px 8px rgba(250, 112, 154, 0.3);">
                        <i class="fas fa-tasks me-2"></i>
                        Pending: <?php echo $pending_posts; ?>
                    </span>
                </div>
            </div>
        </header>

        <?php if ($message): ?>
        <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($message['text']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <!-- Tab Navigation -->
        <div class="pin-tabs">
            <button class="pin-tab active" data-target="welcome-tab">
                <i class="fas fa-home me-2"></i>
                <span class="text">Welcome</span>
            </button>
            <button class="pin-tab" data-target="new-pins-tab">
                <i class="fas fa-thumbtack me-2"></i>
                <span class="text">New Pins</span>
                <span class="pin-tab-indicator"><?php echo count($new_pins); ?></span>
            </button>
            <button class="pin-tab" data-target="repins-tab">
                <i class="fas fa-redo me-2"></i>
                <span class="text">Re-Pins</span>
                <span class="pin-tab-indicator"><?php echo count($repin_tasks); ?></span>
            </button>
            <button class="pin-tab" data-target="bulk-update-tab">
                <i class="fas fa-layer-group me-2"></i>
                <span class="text">Bulk Update</span>
            </button>
            <button class="pin-tab" data-target="submission-history-tab">
                <i class="fas fa-history me-2"></i>
                <span class="text">Submission History</span>
            </button>
            <button class="pin-tab" data-target="import-tab">
                <i class="fas fa-file-import me-2"></i>
                <span class="text">Import</span>
            </button>
            <button class="pin-tab" data-target="ignored-posts-tab">
                <i class="fas fa-eye-slash me-2"></i>
                <span class="text">Ignored Posts</span>
                <span class="pin-tab-indicator"><?php echo $ignored_posts_count; ?></span>
            </button>
        </div>

        <!-- Welcome Page Section -->
        <div id="welcome-tab" class="pin-tab-content active">
            <div class="card">
                <div class="card-header">
                    <h3 class="h5 mb-0">
                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                        Dashboard Overview
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Statistics Dashboard Section -->
                    <div class="statistics-dashboard mb-4">
                        <div class="row g-3 mb-4">
                            <!-- Status Distribution Cards -->
                            <div class="col-md-2 col-sm-4 col-6">
                                <div class="stat-card bg-secondary text-white clickable-stat-card" data-status="0" style="cursor: pointer;">
                                    <div class="stat-icon">
                                        <i class="fas fa-circle"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number"><?php echo $status_counts[0]; ?></div>
                                        <div class="stat-label">Not Pinned</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-4 col-6">
                                <div class="stat-card bg-primary text-white clickable-stat-card" data-status="1" style="cursor: pointer;">
                                    <div class="stat-icon">
                                        <i class="fas fa-thumbtack"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number"><?php echo $status_counts[1]; ?></div>
                                        <div class="stat-label">1 Pin Done</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-4 col-6">
                                <div class="stat-card bg-primary text-white clickable-stat-card" data-status="2" style="cursor: pointer;">
                                    <div class="stat-icon">
                                        <i class="fas fa-thumbtack"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number"><?php echo $status_counts[2]; ?></div>
                                        <div class="stat-label">2 Pins Done</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-4 col-6">
                                <div class="stat-card bg-primary text-white clickable-stat-card" data-status="3" style="cursor: pointer;">
                                    <div class="stat-icon">
                                        <i class="fas fa-thumbtack"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number"><?php echo $status_counts[3]; ?></div>
                                        <div class="stat-label">3 Pins Done</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-4 col-6">
                                <div class="stat-card bg-primary text-white clickable-stat-card" data-status="4" style="cursor: pointer;">
                                    <div class="stat-icon">
                                        <i class="fas fa-thumbtack"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number"><?php echo $status_counts[4]; ?></div>
                                        <div class="stat-label">4 Pins Done</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-4 col-6">
                                <div class="stat-card bg-success text-white clickable-stat-card" data-status="5" style="cursor: pointer;">
                                    <div class="stat-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number"><?php echo $status_counts[5]; ?></div>
                                        <div class="stat-label">5 PINS DONE</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Ignored Posts Card -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-3 col-sm-6">
                                <div class="stat-card bg-warning text-dark clickable-stat-card" data-target="ignored-posts-tab" style="cursor: pointer;">
                                    <div class="stat-icon">
                                        <i class="fas fa-eye-slash"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number"><?php echo $ignored_posts_count; ?></div>
                                        <div class="stat-label">Ignored Posts</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Summary Statistics -->
                        <div class="row g-3 mb-4">
                            <div class="col-lg-3 col-md-6">
                                <div class="summary-card">
                                    <div class="summary-icon bg-info">
                                        <i class="fas fa-list"></i>
                                    </div>
                                    <div class="summary-content">
                                        <div class="summary-number"><?php echo $total_posts; ?></div>
                                        <div class="summary-label">Total Posts</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <div class="summary-card">
                                    <div class="summary-icon bg-success">
                                        <i class="fas fa-percentage"></i>
                                    </div>
                                    <div class="summary-content">
                                        <div class="summary-number"><?php echo $completion_percentage; ?>%</div>
                                        <div class="summary-label">Completion Rate</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <div class="summary-card">
                                    <div class="summary-icon bg-primary">
                                        <i class="fas fa-calendar-week"></i>
                                    </div>
                                    <div class="summary-content">
                                        <div class="summary-number"><?php echo $weekly_pins; ?></div>
                                        <div class="summary-label">Weekly Pins</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <div class="summary-card">
                                    <div class="summary-icon bg-warning">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div class="summary-content">
                                        <div class="summary-number"><?php echo $monthly_pins; ?></div>
                                        <div class="summary-label">Monthly Pins</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="progress-section mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-medium">Overall Completion Progress</span>
                                <span class="text-muted"><?php echo $posts_with_progress; ?> of <?php echo $total_posts; ?> posts completed</span>
                            </div>
                            <div class="progress" style="height: 10px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $progress_percentage; ?>%" aria-valuenow="<?php echo $progress_percentage; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>

                        <!-- Email Report Section -->
                        <div class="email-report-section mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <h5 class="mb-1">📧 Daily Email Report</h5>
                                    <p class="text-muted mb-0 small">Send today's pin activity summary to your email</p>
                                </div>
                                <button id="send-email-btn" class="btn btn-primary" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                                                                        border: none; border-radius: 8px; padding: 10px 20px;
                                                                                        font-weight: 500; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                                                                                        transition: all 0.3s ease;">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    <span class="btn-text">Send Report</span>
                                </button>
                            </div>

                            <!-- Email Status Alert -->
                            <div id="email-alert" class="alert" style="display: none;" role="alert"></div>

                            <!-- Quick Stats Preview -->
                            <div class="row g-2">
                                <div class="col-6 col-md-3">
                                    <div class="text-center p-2 bg-light rounded">
                                        <small class="text-muted d-block">Today's Pins</small>
                                        <strong class="text-primary"><?php echo $today_pin_count; ?></strong>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3">
                                    <div class="text-center p-2 bg-light rounded">
                                        <small class="text-muted d-block">Not Pinned</small>
                                        <strong class="text-secondary"><?php echo $status_counts[0]; ?></strong>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3">
                                    <div class="text-center p-2 bg-light rounded">
                                        <small class="text-muted d-block">Send To</small>
                                        <strong class="text-info">4 Recipients</strong>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3">
                                    <div class="text-center p-2 bg-light rounded">
                                        <small class="text-muted d-block">Last Sent</small>
                                        <strong class="text-success" id="last-sent-time">Never</strong>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!-- New Pins Section -->
        <div id="new-pins-tab" class="pin-tab-content">
            <!-- Alert Container -->
            <div class="alert-container"></div>

            <!-- New Pins Statistics Dashboard -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="h5 mb-0">
                        <i class="fas fa-chart-bar me-2 text-success"></i>
                        New Pins Overview
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row g-3 mb-3">
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-success">
                                    <i class="fas fa-thumbtack"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo count($new_pins); ?></div>
                                    <div class="summary-label">New Pins Available</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        $today_posts = array_filter($new_pins, function($pin) { return $pin['days_old'] == 0; });
                                        echo count($today_posts);
                                        ?>
                                    </div>
                                    <div class="summary-label">Published Today</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-info">
                                    <i class="fas fa-calendar-week"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        $week_posts = array_filter($new_pins, function($pin) { return $pin['days_old'] <= 7; });
                                        echo count($week_posts);
                                        ?>
                                    </div>
                                    <div class="summary-label">This Week</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-primary">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        $priority_posts = array_filter($new_pins, function($pin) { return $pin['days_old'] <= 30; });
                                        echo count($priority_posts);
                                        ?>
                                    </div>
                                    <div class="summary-label">High Priority</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($new_pins)): ?>
                    <div class="progress-section">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-medium">Daily Pin Progress</span>
                            <span class="text-muted"><?php echo $submitted_today; ?> of <?php echo $initial_quota; ?> pins submitted today</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-success" role="progressbar"
                                 style="width: <?php echo $initial_quota > 0 ? ($submitted_today / $initial_quota) * 100 : 0; ?>%"
                                 aria-valuenow="<?php echo $submitted_today; ?>"
                                 aria-valuemin="0"
                                 aria-valuemax="<?php echo $initial_quota; ?>"></div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <form method="post" id="newPinsForm" class="mb-5">
                <input type="hidden" name="form_type" value="daily">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="h5 mb-0">
                                <i class="fas fa-thumbtack me-2 text-success"></i>
                                New Pins for Today
                            </h3>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-sm btn-outline-success select-all-new">
                                    <i class="fas fa-check-double me-1"></i>Select All
                                </button>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-filter me-1"></i>Filter
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item filter-new-pins" href="#" data-filter="today">Published Today</a></li>
                                        <li><a class="dropdown-item filter-new-pins" href="#" data-filter="week">This Week</a></li>
                                        <li><a class="dropdown-item filter-new-pins" href="#" data-filter="month">This Month</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item filter-new-pins" href="#" data-filter="all">Show All</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($new_pins)): ?>
                            <div class="items-count">
                                Showing <span class="fw-medium"><?php echo count($new_pins); ?></span> posts
                            </div>
                            <div class="new-pins-list">
                                <?php foreach ($new_pins as $index => $pin): ?>
                                    <div class="task-item" data-page="1">
                                        <div class="d-flex align-items-start">
                                            <div class="form-check">
                                                <input class="form-check-input new-pin-checkbox"
                                                    type="checkbox"
                                                    name="new_pin[]"
                                                    value="<?php echo $pin['id']; ?>"
                                                    id="new_<?php echo $pin['id']; ?>">
                                            </div>
                                            <div class="ms-3 flex-grow-1">
                                                <h5 class="task-item-title">
                                                    <?php echo htmlspecialchars($pin['title']); ?>
                                                </h5>
                                                <a href="<?php echo $pin['url']; ?>" class="task-item-url" target="_blank">
                                                    <?php echo htmlspecialchars($pin['url']); ?>
                                                </a>
                                                <div class="d-flex justify-content-between align-items-center mt-2">
                                                    <div>
                                                        <span class="badge bg-primary text-white px-2 py-1 me-2">
                                                            <i class="fas fa-calendar me-1"></i>
                                                            <?php echo $pin['publish_date']; ?>
                                                        </span>
                                                        <span class="badge bg-warning text-dark px-2 py-1">
                                                            <i class="fas fa-clock me-1"></i>
                                                            <?php echo $pin['days_old']; ?> days old
                                                        </span>
                                                    </div>
                                                    <div class="d-flex align-items-center">
                                                        <span class="me-2 text-muted small">Not Pinned</span>
                                                        <div class="pin-progress">
                                                            <div class="pin-indicator active">1</div>
                                                            <div class="pin-indicator">2</div>
                                                            <div class="pin-indicator">3</div>
                                                            <div class="pin-indicator">4</div>
                                                            <div class="pin-indicator">5</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mt-3">
                                                    <div class="input-group input-group-sm">
                                                        <input type="url"
                                                            name="pin_link[<?php echo $pin['id']; ?>]"
                                                            class="form-control url-input-enhanced"
                                                            placeholder="Enter Pinterest pin URL...">
                                                        <button type="button" class="btn btn-sm update-single-pin" data-post-id="<?php echo $pin['id']; ?>"
                                                            style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                                                   border: none; color: white; border-radius: 6px;
                                                                   padding: 6px 12px; font-weight: 500;
                                                                   box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
                                                                   transition: all 0.2s ease;">
                                                            <i class="fas fa-upload me-1"></i>Update
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <nav aria-label="New Pins Pagination">
                                <ul class="pagination mini-pagination" id="new-pins-pagination"></ul>
                            </nav>
                            <button type="button" class="btn btn-success mt-3 btn-submit" id="new-pins-submit">
                                <i class="fas fa-check me-2"></i>Mark Selected as Pinned
                            </button>
                        <?php else: ?>
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                No new pins to schedule for today.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>

        <!-- Re-Pin Section -->
        <div id="repins-tab" class="pin-tab-content">
            <!-- Alert Container -->
            <div class="alert-container"></div>

            <!-- Re-Pins Statistics Dashboard -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="h5 mb-0">
                        <i class="fas fa-chart-line me-2 text-primary"></i>
                        Re-Pins Overview
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row g-3 mb-3">
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-primary">
                                    <i class="fas fa-redo"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo count($repin_tasks); ?></div>
                                    <div class="summary-label">Re-Pins Available</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        $urgent_repins = array_filter($repin_tasks, function($repin) { return $repin['days_since_last'] >= 14; });
                                        echo count($urgent_repins);
                                        ?>
                                    </div>
                                    <div class="summary-label">Urgent (14+ days)</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-info">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        if (!empty($repin_tasks)) {
                                            $avg_days = array_sum(array_column($repin_tasks, 'days_since_last')) / count($repin_tasks);
                                            echo round($avg_days, 1);
                                        } else {
                                            echo '0';
                                        }
                                        ?>
                                    </div>
                                    <div class="summary-label">Avg Days Since Last</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-success">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        if (!empty($repin_tasks)) {
                                            $total_progress = array_sum(array_column($repin_tasks, 'pin_count'));
                                            $max_progress = count($repin_tasks) * 5;
                                            echo $max_progress > 0 ? round(($total_progress / $max_progress) * 100) : 0;
                                        } else {
                                            echo '0';
                                        }
                                        ?>%
                                    </div>
                                    <div class="summary-label">Avg Completion</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($repin_tasks)): ?>
                    <div class="progress-section">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-medium">Re-Pin Priority Distribution</span>
                            <span class="text-muted"><?php echo count($urgent_repins); ?> urgent, <?php echo count($repin_tasks) - count($urgent_repins); ?> normal</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <?php
                            $urgent_percentage = count($repin_tasks) > 0 ? (count($urgent_repins) / count($repin_tasks)) * 100 : 0;
                            $normal_percentage = 100 - $urgent_percentage;
                            ?>
                            <div class="progress-bar bg-danger" role="progressbar" style="width: <?php echo $urgent_percentage; ?>%" title="Urgent Re-pins"></div>
                            <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo $normal_percentage; ?>%" title="Normal Re-pins"></div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <form method="post" id="repinsForm" class="mb-5">
                <input type="hidden" name="form_type" value="daily">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="h5 mb-0">
                                <i class="fas fa-redo me-2 text-primary"></i>
                                Re-Pins for Today
                            </h3>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-sm btn-outline-primary select-all-repin">
                                    <i class="fas fa-check-double me-1"></i>Select All
                                </button>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-sort me-1"></i>Sort
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item sort-repins" href="#" data-sort="urgent">Urgent First</a></li>
                                        <li><a class="dropdown-item sort-repins" href="#" data-sort="progress">By Progress</a></li>
                                        <li><a class="dropdown-item sort-repins" href="#" data-sort="date">By Last Pin Date</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item sort-repins" href="#" data-sort="default">Default Order</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($repin_tasks)): ?>
                            <div class="items-count">
                                Showing <span class="fw-medium"><?php echo count($repin_tasks); ?></span> posts
                            </div>
                            <div class="repin-tasks-list">
                                <?php foreach ($repin_tasks as $index => $repin): ?>
                                    <div class="task-item" data-page="1">
                                        <div class="d-flex align-items-start">
                                            <div class="form-check">
                                                <input class="form-check-input repin-checkbox"
                                                    type="checkbox"
                                                    name="repin_done[]"
                                                    value="<?php echo $repin['id']; ?>"
                                                    id="repin_<?php echo $repin['id']; ?>">
                                            </div>
                                            <div class="ms-3 flex-grow-1">
                                                <h5 class="task-item-title">
                                                    <?php echo htmlspecialchars($repin['title']); ?>
                                                </h5>
                                                <a href="<?php echo $repin['url']; ?>" class="task-item-url" target="_blank">
                                                    <?php echo htmlspecialchars($repin['url']); ?>
                                                </a>
                                                <div class="d-flex justify-content-between align-items-center mt-2">
                                                    <div>
                                                        <span class="status-badge bg-info" title="<?php
                                                        $tooltip = [];
                                                        foreach ($repin['pins'] as $pinDetail) {
                                                            $tooltip[] = "Submitted on " . $pinDetail['date'];
                                                        }
                                                        echo implode(", ", $tooltip);
                                                        ?>">
                                                            <i class="fas fa-history"></i>
                                                            <?php echo $repin['days_since_last']; ?> days since last pin
                                                        </span>
                                                    </div>
                                                    <div class="d-flex align-items-center">
                                                        <span class="me-2 text-muted small">
                                                            <?php
                                                            $completed_pins = count($repin['pins']);
                                                            echo "Completed: {$completed_pins} of {$repin['total_pins']}";
                                                            ?>
                                                        </span>
                                                        <div class="pin-progress">
                                                            <?php for ($i = 1; $i <= 5; $i++):
                                                                $completed = ($i < $repin['current_pin']);
                                                                $pin_link = isset($repin['pins'][$i-1]['link']) ? $repin['pins'][$i-1]['link'] : '';
                                                                $pin_date = isset($repin['pins'][$i-1]['date']) ? $repin['pins'][$i-1]['date'] : '';
                                                                if ($completed && $pin_link): ?>
                                                                    <a href="<?php echo $pin_link; ?>" target="_blank" class="pin-indicator completed" title="Submitted on <?php echo $pin_date; ?>"><?php echo $i; ?></a>
                                                                <?php else: ?>
                                                                    <div class="pin-indicator<?php echo $completed ? ' completed' : ''; ?>" <?php if($completed && $pin_date) echo 'title="Submitted on ' . $pin_date . '"'; ?>><?php echo $i; ?></div>
                                                                <?php endif;
                                                            endfor; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mt-3">
                                                    <div class="input-group input-group-sm">
                                                        <input type="url"
                                                            name="repin_override[<?php echo $repin['id']; ?>]"
                                                            class="form-control url-input-enhanced"
                                                            placeholder="Enter Pinterest pin URL...">
                                                        <button type="button" class="btn btn-sm update-single-repin" data-post-id="<?php echo $repin['id']; ?>"
                                                            style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                                                   border: none; color: white; border-radius: 6px;
                                                                   padding: 6px 12px; font-weight: 500;
                                                                   box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
                                                                   transition: all 0.2s ease;">
                                                            <i class="fas fa-upload me-1"></i>Update
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-manage-history" data-post-id="<?php echo $repin['id']; ?>"
                                                            style="background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
                                                                   border: none; color: white; border-radius: 6px;
                                                                   padding: 6px 12px; font-weight: 500;
                                                                   box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
                                                                   transition: all 0.2s ease;">
                                                            <i class="fas fa-history me-1"></i>Manage Pin History
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <nav aria-label="Repins Pagination">
                                <ul class="pagination mini-pagination" id="repins-pagination"></ul>
                            </nav>
                            <button type="button" class="btn btn-primary mt-3 btn-submit" id="repins-submit">
                                <i class="fas fa-check me-2"></i>Mark Selected as Pinned
                            </button>
                        <?php else: ?>
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                No re-pins to schedule for today.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>

        <!-- Bulk Update Section -->
        <div id="bulk-update-tab" class="pin-tab-content">
            <!-- Alert Container -->
            <div class="alert-container"></div>

            <!-- Bulk Update Statistics Dashboard -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="h5 mb-0">
                        <i class="fas fa-chart-pie me-2 text-info"></i>
                        Bulk Operations Overview
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row g-3 mb-3">
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-info">
                                    <i class="fas fa-list"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo count($posts) - count($ignored_posts); ?></div>
                                    <div class="summary-label">Total Posts</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-secondary">
                                    <i class="fas fa-circle"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo $status_counts[0]; ?></div>
                                    <div class="summary-label">Not Pinned</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-primary">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo $posts_with_progress; ?></div>
                                    <div class="summary-label">In Progress</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo $status_counts[5]; ?></div>
                                    <div class="summary-label">Completed</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Action Cards -->
                    <div class="row g-3 mb-3">
                        <div class="col-md-3">
                            <div class="quick-action-card clickable-stat-card" data-status="0" style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <div class="quick-action-icon bg-secondary">
                                        <i class="fas fa-filter"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="quick-action-title">Filter Not Pinned</div>
                                        <div class="quick-action-subtitle">Show only unpinned posts</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="quick-action-card clickable-stat-card" data-status="5" style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <div class="quick-action-icon bg-success">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="quick-action-title">Filter Completed</div>
                                        <div class="quick-action-subtitle">Show completed posts</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="quick-action-card" id="bulk-select-all-action" style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <div class="quick-action-icon bg-primary">
                                        <i class="fas fa-check-double"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="quick-action-title">Select All Visible</div>
                                        <div class="quick-action-subtitle">Select all filtered posts</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="quick-action-card" id="clear-all-filters" style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <div class="quick-action-icon bg-warning">
                                        <i class="fas fa-times"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="quick-action-title">Clear Filters</div>
                                        <div class="quick-action-subtitle">Show all posts</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="progress-section">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-medium">Overall Progress Distribution</span>
                            <span class="text-muted"><?php echo $progress_percentage; ?>% progress rate</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-success" role="progressbar"
                                 style="width: <?php echo $progress_percentage; ?>%"
                                 title="Posts with Progress"></div>
                        </div>
                    </div>
                </div>
            </div>

            <form method="post" id="bulk-update-form">
                <input type="hidden" name="form_type" value="bulk_table">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="h5 mb-0">
                                <i class="fas fa-layer-group me-2 text-info"></i>
                                Bulk Pin Status Update
                            </h3>
                            <div class="d-flex gap-2">
                                <div class="date-filter-container">
                                    <input type="text" id="date-range-filter" class="form-control form-control-sm date-filter-input" placeholder="Filter by date...">
                                    <button type="button" class="clear-date" title="Clear">X</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Filter Status Display -->
                        <div id="filter-status" class="alert alert-info" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <span id="filter-message"></span>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="clear-filter">
                                    <i class="fas fa-times me-1"></i>Show All Posts
                                </button>
                            </div>
                        </div>

                        <div class="mb-3 d-flex align-items-center gap-2" id="bulk-actions-container">
                            <button type="button" id="bulk-clear-pins-btn" class="btn btn-sm btn-warning" disabled style="display: none;">
                                <i class="fas fa-eraser me-1"></i>Clear Pin History
                            </button>
                            <button type="button" id="bulk-ignore-btn" class="btn btn-sm btn-danger" disabled style="display: none;">
                                <i class="fas fa-ban me-1"></i>Ignore Selected
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table id="bulkTable" class="table table-striped">
                                <thead>
                                    <tr>
                                        <th width="40px">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="select-all">
                                            </div>
                                        </th>
                                        <th>Title</th>
                                        <th>Published</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php foreach ($posts as $post):
                                    $post_id = $post->ID;
                                    if (in_array($post_id, $ignored_posts)) continue;
                                    $publish_date = get_the_date('Y-m-d', $post_id);
                                    $current_status = 0;
                                    if(isset($pin_history[$post_id]) && !empty($pin_history[$post_id]['pins'])) {
                                        $current_status = count($pin_history[$post_id]['pins']);
                                        if($current_status > 5) $current_status = 5;
                                    }
                                    switch($current_status) {
                                        case 0:
                                            $status_class = 'bg-secondary';
                                            $status_text = 'Not Pinned';
                                            break;
                                        case 5:
                                            $status_class = 'bg-success';
                                            $status_text = 'Complete';
                                            break;
                                        default:
                                            $status_class = 'bg-primary';
                                            $status_text = "Completed: {$current_status} of 5";
                                            break;
                                    }
                                ?>
                                    <tr data-date="<?php echo $publish_date; ?>">
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input bulk-check" type="checkbox" value="<?php echo $post_id; ?>">
                                            </div>
                                        </td>
                                        <td>
                                            <a href="<?php echo get_permalink($post_id); ?>" target="_blank" class="fw-medium">
                                                <?php echo get_the_title($post_id); ?>
                                            </a>
                                            <?php if(isset($pin_history[$post_id]) && !empty($pin_history[$post_id]['pins'])): ?>
                                            <div class="pin-progress mt-2">
                                                <?php for ($i = 1; $i <= 5; $i++):
                                                    $completed = ($i <= $current_status);
                                                    $pin_link = isset($pin_history[$post_id]['pins'][$i-1]['link']) ? $pin_history[$post_id]['pins'][$i-1]['link'] : '';
                                                    $pin_date = isset($pin_history[$post_id]['pins'][$i-1]['date']) ? $pin_history[$post_id]['pins'][$i-1]['date'] : '';
                                                    if ($completed && $pin_link): ?>
                                                        <a href="<?php echo $pin_link; ?>" target="_blank" class="pin-indicator completed" title="Submitted on <?php echo $pin_date; ?>"><?php echo $i; ?></a>
                                                    <?php else: ?>
                                                        <div class="pin-indicator<?php echo $completed ? ' completed' : ''; ?>" <?php if($completed && $pin_date) echo 'title="Submitted on ' . $pin_date . '"'; ?>><?php echo $i; ?></div>
                                                    <?php endif;
                                                endfor; ?>
                                            </div>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $publish_date; ?></td>
                                        <td>
                                            <span class="status-badge <?php echo $status_class; ?>">
                                                <i class="fas fa-thumbtack me-1"></i><?php echo $status_text; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="bulk-action-form">
                                                <!-- Status Select Group -->
                                                <div class="input-group input-group-sm mb-2">
                                                    <select name="update_status[<?php echo $post_id; ?>]" class="form-select form-select-sm select-enhanced">
                                                        <option value="0" <?php echo $current_status == 0 ? 'selected' : ''; ?>>Not Pinned</option>
                                                        <option value="1" <?php echo $current_status == 1 ? 'selected' : ''; ?>>Completed: 1 of 5</option>
                                                        <option value="2" <?php echo $current_status == 2 ? 'selected' : ''; ?>>Completed: 2 of 5</option>
                                                        <option value="3" <?php echo $current_status == 3 ? 'selected' : ''; ?>>Completed: 3 of 5</option>
                                                        <option value="4" <?php echo $current_status == 4 ? 'selected' : ''; ?>>Completed: 4 of 5</option>
                                                        <option value="5" <?php echo $current_status == 5 ? 'selected' : ''; ?>>Complete</option>
                                                    </select>
                                                </div>

                                                <!-- URL Input Group -->
                                                <div class="input-group input-group-sm mb-2">
                                                    <input type="url"
                                                        name="bulk_pin_link[<?php echo $post_id; ?>]"
                                                        class="form-control url-input-enhanced"
                                                        placeholder="Enter Pinterest pin URL..."
                                                        pattern="https:\/\/www\.pinterest\.com\/pin\/.+"
                                                        title="URL must start with https://www.pinterest.com/pin/">
                                                </div>

                                                <!-- Date and Action Group -->
                                                <div class="d-flex gap-2 align-items-center">
                                                    <input type="date"
                                                        name="bulk_pin_date[<?php echo $post_id; ?>]"
                                                        class="form-control form-control-sm flex-grow-1 date-input-enhanced">
                                                    <button type="button" class="btn btn-sm btn-update-individual" data-post-id="<?php echo $post_id; ?>"
                                                        style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                                               border: none; color: white; border-radius: 6px;
                                                               padding: 6px 12px; font-weight: 500;
                                                               box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
                                                               transition: all 0.2s ease;">
                                                        <i class="fas fa-upload me-1"></i>Update
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-manage-history" data-post-id="<?php echo $post_id; ?>"
                                                        style="background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
                                                               border: none; color: white; border-radius: 6px;
                                                               padding: 6px 12px; font-weight: 500;
                                                               box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
                                                               transition: all 0.2s ease;">
                                                        <i class="fas fa-history me-1"></i>Manage Pin History
                                                    </button>
                                                    <form method="post" class="d-inline">
                                                        <input type="hidden" name="form_type" value="ignore_post">
                                                        <input type="hidden" name="post_id" value="<?php echo $post_id; ?>">
                                                        <button type="submit" class="btn btn-sm" title="Ignore this post"
                                                            style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
                                                                   border: 1px solid #f8b500; color: #d63031; border-radius: 6px;
                                                                   padding: 6px 10px; font-weight: 500;
                                                                   box-shadow: 0 2px 4px rgba(248, 181, 0, 0.2);
                                                                   transition: all 0.2s ease;">
                                                            <i class="fas fa-ban"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Submission History Section -->
        <div id="submission-history-tab" class="pin-tab-content">
            <!-- Alert Container -->
            <div class="alert-container"></div>

            <!-- Submission History Analytics Dashboard -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="h5 mb-0">
                        <i class="fas fa-chart-area me-2 text-success"></i>
                        Submission Analytics
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row g-3 mb-3">
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-success">
                                    <i class="fas fa-thumbtack"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo count($submission_history); ?></div>
                                    <div class="summary-label">Total Submissions</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-primary">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        $today_submissions = array_filter($submission_history, function($sub) use ($today_str) {
                                            return $sub['pin_date'] == $today_str;
                                        });
                                        echo count($today_submissions);
                                        ?>
                                    </div>
                                    <div class="summary-label">Today's Submissions</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-info">
                                    <i class="fas fa-calendar-minus"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        $yesterday_str = date('Y-m-d', strtotime('-1 day'));
                                        $yesterday_submissions = array_filter($submission_history, function($sub) use ($yesterday_str) {
                                            return $sub['pin_date'] == $yesterday_str;
                                        });
                                        echo count($yesterday_submissions);
                                        ?>
                                    </div>
                                    <div class="summary-label">Yesterday's Submissions</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-warning">
                                    <i class="fas fa-calendar-week"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo $weekly_pins; ?></div>
                                    <div class="summary-label">This Week</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Time Frame Statistics -->
                    <div class="row g-3 mb-3">
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-success">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo $monthly_pins; ?></div>
                                    <div class="summary-label">This Month</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-secondary">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        $last_7_days = date('Y-m-d', strtotime('-7 days'));
                                        $last_7_submissions = array_filter($submission_history, function($sub) use ($last_7_days, $today_str) {
                                            return $sub['pin_date'] >= $last_7_days && $sub['pin_date'] <= $today_str;
                                        });
                                        echo count($last_7_submissions);
                                        ?>
                                    </div>
                                    <div class="summary-label">Last 7 Days</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white;">
                                    <i class="fas fa-calendar-plus"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        $last_15_days = date('Y-m-d', strtotime('-15 days'));
                                        $last_15_submissions = array_filter($submission_history, function($sub) use ($last_15_days, $today_str) {
                                            return $sub['pin_date'] >= $last_15_days && $sub['pin_date'] <= $today_str;
                                        });
                                        echo count($last_15_submissions);
                                        ?>
                                    </div>
                                    <div class="summary-label">Last 15 Days</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-dark">
                                    <i class="fas fa-calendar-times"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        $last_30_days = date('Y-m-d', strtotime('-30 days'));
                                        $last_30_submissions = array_filter($submission_history, function($sub) use ($last_30_days, $today_str) {
                                            return $sub['pin_date'] >= $last_30_days && $sub['pin_date'] <= $today_str;
                                        });
                                        echo count($last_30_submissions);
                                        ?>
                                    </div>
                                    <div class="summary-label">Last 30 Days</div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- Quick Analytics Charts -->
                    <div class="row g-3 mb-3">
                        <div class="col-md-12">
                            <div class="analytics-chart">
                                <div class="chart-header d-flex justify-content-between align-items-center">
                                    <span class="chart-title fw-medium">Daily Average</span>
                                    <span class="chart-value badge" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                                                          color: white; padding: 6px 12px; border-radius: 12px;
                                                                          font-size: 0.875rem; font-weight: 500;">
                                        <?php
                                        if ($monthly_pins > 0) {
                                            echo round($monthly_pins / date('j'), 1);
                                        } else {
                                            echo '0';
                                        }
                                        ?> pins/day
                                    </span>
                                </div>
                                <div class="progress mt-3" style="height: 10px; border-radius: 10px;">
                                    <div class="progress-bar" role="progressbar"
                                         style="width: <?php echo min(100, ($monthly_pins / date('j')) * 10); ?>%;
                                                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                                border-radius: 10px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="progress-section">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-medium">Monthly Progress</span>
                            <span class="text-muted"><?php echo $monthly_pins; ?> pins this month</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-success" role="progressbar"
                                 style="width: <?php echo min(100, ($monthly_pins / ($initial_quota * date('j'))) * 100); ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="h5 mb-0">
                            <i class="fas fa-history me-2"></i>
                            Submission History
                        </h3>
                        <div class="d-flex gap-2">
                            <select id="history-filter-select" class="form-select form-select-sm" style="width: auto;">
                                <option value="all">Show All</option>
                                <option value="today">Today</option>
                                <option value="yesterday">Yesterday</option>
                                <option value="last7">Last 7 Days</option>
                                <option value="last15">Last 15 Days</option>
                                <option value="thismonth">This Month</option>
                                <option value="lastmonth">Last Month</option>
                                <option value="last3months">Last 3 months</option>
                                <option value="last6months">Last 6 months</option>
                                <option value="lastyear">Last year</option>
                                <option value="custom">Custom Range</option>
                            </select>
                            <input type="text" id="history-custom-range" class="form-control form-control-sm" style="display:none;" placeholder="Select date range">
                            <button type="button" id="export-csv" class="btn btn-sm btn-export-csv">
                                <i class="fas fa-download me-1"></i>Export CSV
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($submission_history)): ?>
                        <div class="table-responsive">
                            <table id="historyTable" class="table table-striped">
                                <thead>
                                    <tr>
                                        <th width="60">#</th>
                                        <th>Post Title</th>
                                        <th>Pin Date</th>
                                        <th>Pin Link</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $row_number = 1;
                                    foreach ($submission_history as $log): ?>
                                        <tr data-date="<?php echo $log['pin_date']; ?>">
                                            <td class="text-center">
                                                <span class="row-number-badge">
                                                    <?php echo $row_number++; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?php echo $log['url']; ?>" target="_blank" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($log['title']); ?>
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge" style="background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
                                                                           color: white; padding: 6px 12px; border-radius: 8px;
                                                                           font-size: 0.8rem; font-weight: 500;
                                                                           box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);">
                                                    <i class="fas fa-calendar-alt me-1"></i>
                                                    <?php echo $log['pin_date']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?php echo $log['pin_link']; ?>" target="_blank" class="btn btn-sm btn-view-pin">
                                                    <i class="fas fa-external-link-alt me-1"></i>View Pin
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            No submission history available.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Import Pin History Section -->
        <div id="import-tab" class="pin-tab-content">
            <!-- Alert Container -->
            <div class="alert-container"></div>

            <!-- Import Statistics Dashboard -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="h5 mb-0">
                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                        Import Overview
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row g-3 mb-3">
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-primary">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo count($pin_history); ?></div>
                                    <div class="summary-label">Posts in Database</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        $posts_with_pins = array_filter($pin_history, function($post) {
                                            return !empty($post['pins']);
                                        });
                                        echo count($posts_with_pins);
                                        ?>
                                    </div>
                                    <div class="summary-label">Posts with Pins</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-info">
                                    <i class="fas fa-thumbtack"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        $total_pins = 0;
                                        foreach ($pin_history as $post) {
                                            $total_pins += count($post['pins']);
                                        }
                                        echo $total_pins;
                                        ?>
                                    </div>
                                    <div class="summary-label">Total Pin Records</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number">
                                        <?php
                                        if (!empty($pin_history)) {
                                            $dates = array();
                                            foreach ($pin_history as $post) {
                                                if (!empty($post['pins'])) {
                                                    foreach ($post['pins'] as $pin) {
                                                        $dates[] = $pin['date'];
                                                    }
                                                }
                                            }
                                            if (!empty($dates)) {
                                                $latest_date = max($dates);
                                                $days_ago = (strtotime($today_str) - strtotime($latest_date)) / (60 * 60 * 24);
                                                echo floor($days_ago);
                                            } else {
                                                echo 'N/A';
                                            }
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </div>
                                    <div class="summary-label">Days Since Last Import</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="progress-section">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-medium">Database Coverage</span>
                            <span class="text-muted"><?php echo count($posts_with_pins); ?> of <?php echo count($pin_history); ?> posts have pin data</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-primary" role="progressbar"
                                 style="width: <?php echo count($pin_history) > 0 ? (count($posts_with_pins) / count($pin_history)) * 100 : 0; ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Import Wizard -->
            <div class="card">
                <div class="card-header">
                    <h3 class="h5 mb-0">
                        <i class="fas fa-file-import me-2 text-primary"></i>
                        Import Pin History
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Import Steps -->
                    <div class="import-steps mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="step-card active" id="step-1">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <div class="step-title">Select File</div>
                                        <div class="step-description">Choose your CSV file</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="step-card" id="step-2">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <div class="step-title">Validate</div>
                                        <div class="step-description">Check file format</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="step-card" id="step-3">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <div class="step-title">Import</div>
                                        <div class="step-description">Process the data</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File Upload Section -->
                    <div class="import-section" id="upload-section">
                        <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                            <input type="hidden" name="form_type" value="csv_import">
                            <div class="mb-4">
                                <label for="csv_file" class="form-label fw-medium">Upload CSV File</label>
                                <div class="file-upload-area" id="file-upload-area">
                                    <div class="file-upload-content">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <h5>Drag & Drop your CSV file here</h5>
                                        <p class="text-muted">or click to browse</p>
                                        <input type="file" class="form-control d-none" id="csv_file" name="csv_file" accept=".csv" required>
                                        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('csv_file').click()">
                                            <i class="fas fa-folder-open me-2"></i>Choose File
                                        </button>
                                    </div>
                                </div>
                                <div class="file-info mt-3" id="file-info" style="display: none;">
                                    <div class="alert alert-info">
                                        <i class="fas fa-file-csv me-2"></i>
                                        <span id="file-name"></span> - <span id="file-size"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Format Requirements -->
                            <div class="format-requirements mb-4">
                                <h6 class="fw-medium mb-3">
                                    <i class="fas fa-info-circle me-2 text-info"></i>
                                    Supported CSV Formats
                                </h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="requirement-card">
                                            <h6 class="text-primary">
                                                <i class="fas fa-download me-2"></i>Export Format (Recommended)
                                            </h6>
                                            <p class="small text-muted mb-2">Use exported CSV files directly</p>
                                            <ul class="list-unstyled mb-0">
                                                <li><code>Post Title</code> - Post title</li>
                                                <li><code>Pin Date</code> - Pin submission date</li>
                                                <li><code>Pin Link</code> - Pinterest URL</li>
                                            </ul>
                                            <div class="mt-2">
                                                <span class="badge bg-success">Auto-detected</span>
                                                <span class="badge bg-info">One pin per row</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="requirement-card">
                                            <h6 class="text-secondary">
                                                <i class="fas fa-upload me-2"></i>Legacy Format
                                            </h6>
                                            <p class="small text-muted mb-2">Traditional import format</p>
                                            <ul class="list-unstyled mb-0">
                                                <li><code>Title</code> - Post title</li>
                                                <li><code>URL</code> - Post URL</li>
                                                <li><code>Publish Date</code> - Publication date</li>
                                                <li><code>Pin1 Link</code>, <code>Pin1 Date</code>, etc.</li>
                                            </ul>
                                            <div class="mt-2">
                                                <span class="badge bg-secondary">Auto-detected</span>
                                                <span class="badge bg-warning">One post per row</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    <strong>Tip:</strong> You can now import CSV files exported from the Submission History tab directly!
                                    The system automatically detects the format and processes accordingly.
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <div class="text-muted">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Your data is processed securely and not shared with third parties.
                                </div>
                                <button type="submit" class="btn btn-primary" id="import-btn" disabled>
                                    <i class="fas fa-upload me-2"></i>Import Data
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ignored Posts Section -->
        <div id="ignored-posts-tab" class="pin-tab-content">
            <!-- Alert Container -->
            <div class="alert-container"></div>

            <!-- Ignored Posts Overview Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="h5 mb-0">
                        <i class="fas fa-eye-slash me-2 text-warning"></i>
                        Ignored Posts Overview
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row g-3 mb-3">
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-warning">
                                    <i class="fas fa-eye-slash"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo $ignored_posts_count; ?></div>
                                    <div class="summary-label">Total Ignored</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-info">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo !empty($ignored_posts_details) ? count(array_filter($ignored_posts_details, function($post) { return date('Y-m-d', strtotime($post['ignored_date'])) === date('Y-m-d'); })) : 0; ?></div>
                                    <div class="summary-label">Ignored Today</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-primary">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo !empty($ignored_posts_details) ? count(array_filter($ignored_posts_details, function($post) { return strtotime($post['ignored_date']) >= strtotime('-7 days'); })) : 0; ?></div>
                                    <div class="summary-label">This Week</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="summary-card">
                                <div class="summary-icon bg-success">
                                    <i class="fas fa-undo"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number"><?php echo $ignored_posts_count; ?></div>
                                    <div class="summary-label">Available to Restore</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Action Cards -->
                    <div class="row g-3 mb-3">
                        <div class="col-md-3">
                            <div class="quick-action-card" id="select-all-ignored-action" style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <div class="quick-action-icon bg-primary">
                                        <i class="fas fa-check-double"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="quick-action-title">Select All Visible</div>
                                        <div class="quick-action-subtitle">Select all filtered posts</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="quick-action-card" id="clear-ignored-selection" style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <div class="quick-action-icon bg-secondary">
                                        <i class="fas fa-times"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="quick-action-title">Clear Selection</div>
                                        <div class="quick-action-subtitle">Deselect all posts</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="quick-action-card" id="restore-all-ignored-action" style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <div class="quick-action-icon bg-success">
                                        <i class="fas fa-undo"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="quick-action-title">Restore All</div>
                                        <div class="quick-action-subtitle">Restore all ignored posts</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="quick-action-card" id="search-ignored-posts" style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <div class="quick-action-icon bg-info">
                                        <i class="fas fa-search"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="quick-action-title">Search Posts</div>
                                        <div class="quick-action-subtitle">Find specific posts</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if ($ignored_posts_count > 0): ?>
                    <div class="progress-section">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-medium">Ignored Posts Distribution</span>
                            <span class="text-muted"><?php echo $ignored_posts_count; ?> posts currently ignored</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-warning" role="progressbar"
                                 style="width: 100%"
                                 title="Ignored Posts"></div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <form method="post" id="ignored-posts-form">
                <input type="hidden" name="form_type" value="bulk_restore_ignored_posts">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h3 class="h5 mb-0">
                                <i class="fas fa-eye-slash me-2 text-warning"></i>
                                Ignored Posts Management
                            </h3>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($ignored_posts_details)): ?>
                            <div class="mb-3 d-flex align-items-center gap-2" id="ignored-bulk-actions-container">
                                <button type="button" id="restore-selected-btn" class="btn btn-sm btn-success" disabled style="display: none;">
                                    <i class="fas fa-undo me-1"></i>Restore Selected
                                </button>
                                <form method="post" class="d-inline">
                                    <input type="hidden" name="form_type" value="restore_all_ignored_posts">
                                    <button type="submit" class="btn btn-success btn-sm"
                                            onclick="return confirm('Are you sure you want to restore all ignored posts? This action cannot be undone.')">
                                        <i class="fas fa-undo me-1"></i>Restore All
                                    </button>
                                </form>
                            </div>

                        <div class="table-responsive">
                            <table id="ignoredPostsTable" class="table table-striped">
                                <thead>
                                    <tr>
                                        <th width="40px">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="select-all-ignored">
                                            </div>
                                        </th>
                                        <th>Title</th>
                                        <th>Published Date</th>
                                        <th>Ignored Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($ignored_posts_details as $post): ?>
                                        <tr data-date="<?php echo date('Y-m-d', strtotime($post['ignored_date'])); ?>">
                                            <td>
                                                <div class="form-check">
                                                    <input class="form-check-input ignored-post-check" type="checkbox" value="<?php echo $post['post_id']; ?>">
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div>
                                                        <h6 class="mb-0 fw-medium"><?php echo esc_html($post['title']); ?></h6>
                                                        <small class="text-muted">ID: <?php echo $post['post_id']; ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="text-muted">
                                                    <?php echo date('M j, Y', strtotime($post['publish_date'])); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="text-warning">
                                                    <i class="fas fa-calendar-times me-1"></i>
                                                    <?php echo date('M j, Y', strtotime($post['ignored_date'])); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <form method="post" class="d-inline">
                                                    <input type="hidden" name="form_type" value="restore_ignored_post">
                                                    <input type="hidden" name="post_id" value="<?php echo $post['post_id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-success"
                                                            title="Restore this post"
                                                            onclick="return confirm('Are you sure you want to restore this post?')">
                                                        <i class="fas fa-undo me-1"></i>Restore
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="fas fa-check-circle fa-3x text-success"></i>
                            </div>
                            <h5 class="text-muted">No Ignored Posts</h5>
                            <p class="text-muted mb-0">
                                Great! You don't have any ignored posts at the moment.
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

    </div>

    <!-- Pin Details Modal -->
    <div class="modal fade" id="pinDetailsModal" tabindex="-1" aria-labelledby="pinDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="pinDetailsModalLabel">Pin Details</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body" id="pinDetailsContent">
            <!-- Details will be loaded here via JS -->
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
        </div>
    </div>
    </div>

    <!-- Pin History Management Modal -->
    <div class="modal fade" id="pinHistoryModal" tabindex="-1" aria-labelledby="pinHistoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="pinHistoryModalLabel">Manage Pin History</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div id="pinHistoryContent">
                <!-- Content will be loaded here via JS -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div id="pinHistoryEditor" style="display: none;">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Edit pin history below. You can add, edit, or remove pins.
                </div>
                <table class="table table-bordered" id="pinHistoryTable">
                    <thead>
                        <tr>
                            <th width="60">#</th>
                            <th>Pinterest URL</th>
                            <th width="150">Date</th>
                            <th width="100">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="pinHistoryTableBody">
                        <!-- Rows will be added dynamically -->
                    </tbody>
                </table>
                <button type="button" id="addPinRow" class="btn btn-sm btn-success mt-2">
                    <i class="fas fa-plus me-1"></i>Add Pin
                </button>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" id="savePinHistory">Save Changes</button>
        </div>
        </div>
    </div>
    </div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

<script>
$(document).ready(function() {
    // Utility: Update the displayed daily quota.
    function updateDailyQuota(delta) {
        // Assumes the daily quota number is in a span with ID daily-quota-value.
        let quotaEl = $('#daily-quota-value');
        if (quotaEl.length) {
            let current = parseInt(quotaEl.text());
            let newVal = current - delta;
            if (newVal < 0) newVal = 0;
            quotaEl.text(newVal);
        }
    }

    // Make custom range input read-only so users must use the picker
    $("#history-custom-range").prop('readonly', true);

    // ===== HYBRID DUPLICATE PREVENTION SYSTEM =====
    // Enterprise-level duplicate input prevention across all tabs

    // Configuration
    const CONFIG = {
        COOLDOWN_PERIOD: 5000,      // 5 seconds
        SUCCESS_DISPLAY_TIME: 3000,  // 3 seconds
        ERROR_DISPLAY_TIME: 2000,    // 2 seconds
        CLEANUP_INTERVAL: 300000,    // 5 minutes
        MAX_SUBMISSION_AGE: 300000   // 5 minutes
    };

    // Global submission tracking
    const recentSubmissions = new Map();

    // Central duplicate detection system
    function isDuplicateSubmission(postId, pinLink, cooldownMs = CONFIG.COOLDOWN_PERIOD) {
        const recentKey = `${postId}_${pinLink}`;
        const now = Date.now();

        if (recentSubmissions.has(recentKey)) {
            const lastSubmission = recentSubmissions.get(recentKey);
            if (now - lastSubmission < cooldownMs) {
                return true;
            }
        }

        recentSubmissions.set(recentKey, now);
        return false;
    }

    // Memory management - cleanup old submissions
    function cleanupOldSubmissions() {
        const now = Date.now();
        const maxAge = CONFIG.MAX_SUBMISSION_AGE;

        for (let [key, timestamp] of recentSubmissions.entries()) {
            if (now - timestamp > maxAge) {
                recentSubmissions.delete(key);
            }
        }
    }

    // Enhanced Pin Update Handler with multi-layer protection
    function handlePinUpdate(button, postId, pinUrlInput, formData, successCallback) {
        const pinLink = pinUrlInput.val().trim();

        // Validation
        if (pinLink === '' || !pinLink.includes('pinterest.com')) {
            showAlert('warning', 'Please enter a valid Pinterest URL.');
            pinUrlInput.addClass('is-invalid');
            return;
        }

        // Duplicate detection
        if (isDuplicateSubmission(postId, pinLink)) {
            showAlert('warning', 'This URL was recently submitted. Please wait before resubmitting.');
            return;
        }

        // UI State: Loading
        const originalButtonHtml = button.html();
        button.prop('disabled', true)
              .removeClass('btn-primary btn-success btn-danger')
              .addClass('btn-secondary')
              .html('<i class="fas fa-spinner fa-spin me-1"></i>Updating...');

        // Remove validation errors
        pinUrlInput.removeClass('is-invalid');

        // Submit request
        $.post(window.location.href, formData, function(response) {
            if(response.success) {
                // Success State
                pinUrlInput.val(''); // Clear input immediately
                button.removeClass('btn-secondary')
                      .addClass('btn-success')
                      .html('<i class="fas fa-check me-1"></i>Updated!');

                // Execute success callback
                if (successCallback) successCallback();

                // Show success alert
                showAlert('success', response.message);

                // Reset button after delay
                setTimeout(() => {
                    button.removeClass('btn-success')
                          .addClass('btn-primary')
                          .html(originalButtonHtml)
                          .prop('disabled', false);
                }, CONFIG.SUCCESS_DISPLAY_TIME);

            } else {
                // Error State - Keep input for retry
                button.removeClass('btn-secondary')
                      .addClass('btn-danger')
                      .html('<i class="fas fa-exclamation-triangle me-1"></i>Error');

                showAlert('danger', response.message);
                pinUrlInput.addClass('is-invalid');

                // Reset button after shorter delay
                setTimeout(() => {
                    button.removeClass('btn-danger')
                          .addClass('btn-primary')
                          .html(originalButtonHtml)
                          .prop('disabled', false);
                }, CONFIG.ERROR_DISPLAY_TIME);
            }
        }, 'json')
        .fail(function(jqXHR, textStatus, errorThrown) {
            // Network Error State
            button.removeClass('btn-secondary')
                  .addClass('btn-danger')
                  .html('<i class="fas fa-wifi me-1"></i>Retry');

            showAlert('danger', 'Network error. Please try again.');

            setTimeout(() => {
                button.removeClass('btn-danger')
                      .addClass('btn-primary')
                      .html(originalButtonHtml)
                      .prop('disabled', false);
            }, CONFIG.ERROR_DISPLAY_TIME);
        });
    }

    // Enhanced Bulk Update Handler with additional date validation
    function handleBulkPinUpdate(button, postId, urlInput, dateInput, formData, successCallback) {
        const pinLink = urlInput.val().trim();
        const pinDate = dateInput.val().trim();

        // URL Validation
        if (pinLink === '' || !pinLink.includes('pinterest.com')) {
            showAlert('warning', 'Please enter a valid Pinterest URL for post ' + postId);
            urlInput.addClass('is-invalid');
            return;
        }

        // Date validation for bulk updates
        if (pinDate === '' || isNaN(Date.parse(pinDate))) {
            showAlert('warning', 'Please enter a valid date for post ' + postId);
            dateInput.addClass('is-invalid');
            return;
        }

        // Duplicate detection
        if (isDuplicateSubmission(postId, pinLink)) {
            showAlert('warning', 'This URL was recently submitted. Please wait before resubmitting.');
            return;
        }

        // UI State: Loading
        const originalButtonHtml = button.html();
        button.prop('disabled', true)
              .removeClass('btn-primary btn-success btn-danger')
              .addClass('btn-secondary')
              .html('<i class="fas fa-spinner fa-spin me-1"></i>Updating...');

        // Remove validation errors
        urlInput.removeClass('is-invalid');
        dateInput.removeClass('is-invalid');

        // Submit request
        $.post(window.location.href, formData, function(response) {
            if(response.success) {
                // Success State
                urlInput.val(''); // Clear URL input immediately
                dateInput.val(''); // Clear date input immediately
                button.removeClass('btn-secondary')
                      .addClass('btn-success')
                      .html('<i class="fas fa-check me-1"></i>Updated!');

                // Execute success callback
                if (successCallback) successCallback();

                // Show success alert
                showAlert('success', response.message);

                // Reset button after delay
                setTimeout(() => {
                    button.removeClass('btn-success')
                          .addClass('btn-primary')
                          .html(originalButtonHtml)
                          .prop('disabled', false);
                }, CONFIG.SUCCESS_DISPLAY_TIME);

            } else {
                // Error State - Keep inputs for retry
                button.removeClass('btn-secondary')
                      .addClass('btn-danger')
                      .html('<i class="fas fa-exclamation-triangle me-1"></i>Error');

                showAlert('danger', response.message);
                urlInput.addClass('is-invalid');

                // Reset button after shorter delay
                setTimeout(() => {
                    button.removeClass('btn-danger')
                          .addClass('btn-primary')
                          .html(originalButtonHtml)
                          .prop('disabled', false);
                }, CONFIG.ERROR_DISPLAY_TIME);
            }
        }, 'json')
        .fail(function(jqXHR, textStatus, errorThrown) {
            // Network Error State
            button.removeClass('btn-secondary')
                  .addClass('btn-danger')
                  .html('<i class="fas fa-wifi me-1"></i>Retry');

            showAlert('danger', 'Network error. Please try again.');

            setTimeout(() => {
                button.removeClass('btn-danger')
                      .addClass('btn-primary')
                      .html(originalButtonHtml)
                      .prop('disabled', false);
            }, CONFIG.ERROR_DISPLAY_TIME);
        });
    }

    // Memory management setup
    setInterval(cleanupOldSubmissions, CONFIG.CLEANUP_INTERVAL);

    // Clean up on page unload
    $(window).on('beforeunload', function() {
        recentSubmissions.clear();
    });

    // Tab Navigation
    $('.pin-tab').on('click', function() {
        const targetId = $(this).data('target');
        $('.pin-tab').removeClass('active');
        $(this).addClass('active');
        $('.pin-tab-content').removeClass('active');
        $('#' + targetId).addClass('active');
    });

    // Status card click-to-filter functionality
    $('.clickable-stat-card').on('click', function() {
        const status = $(this).data('status');
        const target = $(this).data('target');

        // Handle ignored posts card differently
        if (target === 'ignored-posts-tab') {
            // Switch to Ignored Posts tab
            $('.pin-tab').removeClass('active');
            $('.pin-tab[data-target="ignored-posts-tab"]').addClass('active');
            $('.pin-tab-content').removeClass('active');
            $('#ignored-posts-tab').addClass('active');
            return;
        }

        const statusLabels = {
            0: 'Not Pinned',
            1: 'Completed: 1 of 5',
            2: 'Completed: 2 of 5',
            3: 'Completed: 3 of 5',
            4: 'Completed: 4 of 5',
            5: 'Complete'
        };

        // Switch to Bulk Update tab
        $('.pin-tab').removeClass('active');
        $('.pin-tab[data-target="bulk-update-tab"]').addClass('active');
        $('.pin-tab-content').removeClass('active');
        $('#bulk-update-tab').addClass('active');

        // Filter the table by status
        if (typeof bulkTable !== 'undefined') {
            // Clear any existing search
            bulkTable.search('').draw();

            // Apply status filter using custom search function for exact matching
            const searchTerm = statusLabels[status];

            // Remove any existing custom search functions
            $.fn.dataTable.ext.search = $.fn.dataTable.ext.search.filter(function(fn) {
                return fn.name !== 'statusFilter';
            });

            // Add custom search function for exact status matching
            function statusFilter(settings, data, dataIndex) {
                const statusColumn = data[3]; // Status is column 3
                // Extract just the text content, removing HTML tags
                const statusText = $('<div>').html(statusColumn).text().trim();
                return statusText === searchTerm;
            }
            statusFilter.name = 'statusFilter';

            $.fn.dataTable.ext.search.push(statusFilter);
            bulkTable.draw();

            // Show filter status
            $('#filter-status').show();
            $('#filter-message').text(`Showing posts with status: ${statusLabels[status]}`);
        }

        // Visual feedback on clicked card
        $('.clickable-stat-card').removeClass('active');
        $(this).addClass('active');
    });

    // Clear filter functionality
    $('#clear-filter').on('click', function() {
        if (typeof bulkTable !== 'undefined') {
            // Remove custom search functions
            $.fn.dataTable.ext.search = $.fn.dataTable.ext.search.filter(function(fn) {
                return fn.name !== 'statusFilter';
            });
            bulkTable.search('').column(3).search('').draw();
        }
        $('#filter-status').hide();
        $('.clickable-stat-card').removeClass('active');
    });

    // Initialize DataTable for Bulk Update
    var bulkTable = $('#bulkTable').DataTable({
        order: [[2, "desc"]],
        pageLength: 25,
        dom: "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
             "<'row'<'col-sm-12'tr>>" +
             "<'row align-items-center'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        language: {
            search: "_INPUT_",
            searchPlaceholder: "Search posts...",
            lengthMenu: "Show _MENU_ posts",
            info: "Showing _START_ to _END_ of _TOTAL_ posts",
            paginate: {
                next: '<i class="fas fa-chevron-right"></i>',
                previous: '<i class="fas fa-chevron-left"></i>'
            }
        },
        columnDefs: [
            { orderable: false, targets: [0, 4] },
            { className: "align-middle", targets: "_all" }
        ]
    });

    // Initialize DataTable for Submission History
    var historyTable = $('#historyTable').DataTable({
        order: [[2, "desc"]], // Sort by Pin Date column (descending)
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        dom: "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
             "<'row'<'col-sm-12'tr>>" +
             "<'row align-items-center'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        language: {
            search: "_INPUT_",
            searchPlaceholder: "Search submissions...",
            lengthMenu: "Show _MENU_ submissions",
            info: "Showing _START_ to _END_ of _TOTAL_ submissions",
            paginate: {
                next: '<i class="fas fa-chevron-right"></i>',
                previous: '<i class="fas fa-chevron-left"></i>'
            }
        },
        columnDefs: [
            { orderable: false, targets: [0] }, // Disable sorting for row number column
            { className: "align-middle", targets: "_all" }
        ]
    });

    // Function to update row numbers dynamically
    function updateRowNumbers() {
        $('#historyTable tbody tr:visible').each(function(index) {
            $(this).find('.row-number-badge').text(index + 1);
        });
    }

    // Update row numbers after DataTable draws
    historyTable.on('draw', function() {
        updateRowNumbers();
    });

    // Initial row numbering
    updateRowNumbers();

    // Date Range Picker for Bulk Update with autoApply & modern settings
    $('#date-range-filter').daterangepicker({
        autoUpdateInput: false,
        autoApply: false,
        showDropdowns: true,
        ranges: {
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
            'Last 3 Months': [moment().subtract(3, 'months').startOf('month'), moment().endOf('month')],
            'Last 6 Months': [moment().subtract(6, 'months').startOf('month'), moment().endOf('month')],
            'This Year': [moment().startOf('year'), moment().endOf('year')],
            'Last Year': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')]
        },
        locale: {
            format: 'YYYY-MM-DD',
            cancelLabel: 'Clear',
            applyLabel: 'Apply'
        }
    });
    $('#date-range-filter').on('apply.daterangepicker', function(ev, picker) {
        $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
        filterTableByDateRange(picker.startDate.format('YYYY-MM-DD'), picker.endDate.format('YYYY-MM-DD'));
    });
    $('#date-range-filter').on('cancel.daterangepicker', function(ev, picker) {
        clearDateFilter();
    });
    $('.clear-date').on('click', function() {
        clearDateFilter();
    });
    function clearDateFilter() {
        $('#date-range-filter').val('');
        bulkTable.search('').columns().search('').draw();
    }
    function filterTableByDateRange(startDate, endDate) {
        $.fn.dataTable.ext.search.push(
            function(settings, data, dataIndex) {
                var rowDate = $(bulkTable.row(dataIndex).node()).data('date');
                if (!rowDate) return true;
                return (startDate <= rowDate && rowDate <= endDate);
            }
        );
        bulkTable.draw();
        $.fn.dataTable.ext.search.pop();
    }

    // Bulk Update and Ignore Buttons
    $('#select-all').on('change', function() {
        var rows = bulkTable.rows({ 'search': 'applied' }).nodes();
        $('input.bulk-check', rows).prop('checked', this.checked);
        updateBulkActionState();
    });
    $(document).on('change', '.bulk-check', function() {
        updateBulkActionState();
        if (!$(this).prop('checked')) {
            $('#select-all').prop('checked', false);
        }
    });
    $('#bulk-update-btn').on('click', function() {
        var bulkValue = $('#bulk-edit-select').val();
        var checkedBoxes = $('.bulk-check:checked');
        if (!bulkValue) {
            showAlert('warning', 'Please select a status to update.');
            return;
        }
        if (checkedBoxes.length === 0) {
            showAlert('warning', 'Please select at least one post to update.');
            return;
        }
        if (confirm(`Update ${checkedBoxes.length} selected posts?`)) {
            $('#bulk-update-form').submit();
        }
    });
    $('#bulk-ignore-btn').on('click', function() {
        var checkedBoxes = $('.bulk-check:checked');
        if (checkedBoxes.length === 0) {
            showAlert('warning', 'Please select at least one post to ignore.');
            return;
        }
        if (confirm(`Ignore ${checkedBoxes.length} selected posts? This action cannot be undone.`)) {
            checkedBoxes.each(function() {
                var postId = $(this).val();
                $.post(window.location.href, { form_type: 'ignore_post', post_id: postId });
            });
            location.reload();
        }
    });

    $('.select-all-new').on('click', function() {
        $('.new-pin-checkbox').prop('checked', true);
        updateSubmitButtonVisibility();
    });
    $('.select-all-repin').on('click', function() {
        $('.repin-checkbox').prop('checked', true);
        updateSubmitButtonVisibility();
    });

    setupPagination('.new-pins-list .task-item', '#new-pins-pagination', 10);
    setupPagination('.repin-tasks-list .task-item', '#repins-pagination', 10);
    function setupPagination(itemSelector, paginationSelector, itemsPerPage) {
        const $items = $(itemSelector);
        if ($items.length <= itemsPerPage) return;
        const pageCount = Math.ceil($items.length / itemsPerPage);
        let paginationHTML = `<li class="page-item disabled" id="${paginationSelector.substring(1)}-prev">
                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>`;
        for (let i = 1; i <= pageCount; i++) {
            paginationHTML += `<li class="page-item ${i === 1 ? 'active' : ''}" data-page="${i}">
                    <a class="page-link" href="#">${i}</a>
                </li>`;
        }
        paginationHTML += `<li class="page-item" id="${paginationSelector.substring(1)}-next">
                <a class="page-link" href="#">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>`;
        $(paginationSelector).html(paginationHTML);
        showPage(itemSelector, 1, itemsPerPage);
        $(paginationSelector + ' .page-item').on('click', function(e) {
            e.preventDefault();
            if ($(this).hasClass('disabled')) return;
            const page = $(this).data('page');
            if (!page) return;
            $(paginationSelector + ' .page-item').removeClass('active');
            $(this).addClass('active');
            showPage(itemSelector, page, itemsPerPage);
            $(paginationSelector + '-prev').toggleClass('disabled', page === 1);
            $(paginationSelector + '-next').toggleClass('disabled', page === pageCount);
        });
        $(paginationSelector + '-prev').on('click', function(e) {
            e.preventDefault();
            if ($(this).hasClass('disabled')) return;
            const activePage = parseInt($(paginationSelector + ' .page-item.active').data('page'));
            const prevPage = activePage - 1;
            $(paginationSelector + ' .page-item').removeClass('active');
            $(paginationSelector + ` .page-item[data-page="${prevPage}"]`).addClass('active');
            showPage(itemSelector, prevPage, itemsPerPage);
            $(this).toggleClass('disabled', prevPage === 1);
            $(paginationSelector + '-next').removeClass('disabled');
        });
        $(paginationSelector + '-next').on('click', function(e) {
            e.preventDefault();
            if ($(this).hasClass('disabled')) return;
            const activePage = parseInt($(paginationSelector + ' .page-item.active').data('page'));
            const nextPage = activePage + 1;
            $(paginationSelector + ' .page-item').removeClass('active');
            $(paginationSelector + ` .page-item[data-page="${nextPage}"]`).addClass('active');
            showPage(itemSelector, nextPage, itemsPerPage);
            $(this).toggleClass('disabled', nextPage === pageCount);
            $(paginationSelector + '-prev').removeClass('disabled');
        });
    }

    function showPage(itemSelector, page, itemsPerPage) {
        const $items = $(itemSelector);
        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        $items.hide();
        $items.slice(startIndex, endIndex).show();
        $items.each(function(index) {
            if (index >= startIndex && index < endIndex) {
                $(this).attr('data-page', page);
            }
        });
    }

    function updateBulkActionState() {
        var checkedCount = $('.bulk-check:checked').length;
        var hasSelection = checkedCount > 0;

        // Show/hide and enable/disable bulk action buttons based on selection
        $('#bulk-clear-pins-btn, #bulk-ignore-btn').toggle(hasSelection);
        $('#bulk-clear-pins-btn').prop('disabled', !hasSelection);
        $('#bulk-ignore-btn').prop('disabled', !hasSelection);

        var visibleRows = bulkTable.rows({ 'search': 'applied' }).nodes();
        var allChecked = $('.bulk-check:checked', visibleRows).length === visibleRows.length && visibleRows.length > 0;
        $('#select-all').prop('checked', allChecked);
    }

    function updateSubmitButtonVisibility() {
        const newPinsChecked = $('.new-pin-checkbox:checked').length;
        const repinsChecked = $('.repin-checkbox:checked').length;
        if (newPinsChecked > 0) {
            $('#new-pins-submit').addClass('show');
        } else {
            $('#new-pins-submit').removeClass('show');
        }
        if (repinsChecked > 0) {
            $('#repins-submit').addClass('show');
        } else {
            $('#repins-submit').removeClass('show');
        }
    }

    function getActiveTabContainer() {
        const activeTab = $('.pin-tab-content.active');
        if (activeTab.length > 0) {
            return activeTab;
        }
        return $('.dashboard-container');
    }

    function showAlert(type, message) {
        console.log('Showing alert:', type, message);

        // Remove any existing alerts to avoid stacking
        $('.alert').remove();

        const alertHtml = `<div class="alert alert-${type} alert-dismissible fade show position-relative" role="alert" style="margin-bottom: 20px; z-index: 1050; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>`;

        // Get the active tab container
        const activeTab = getActiveTabContainer();

        // Look for dedicated alert container within the active tab first
        const alertContainer = activeTab.find('.alert-container');

        if (alertContainer.length > 0) {
            // Use the dedicated alert container
            alertContainer.html(alertHtml);
            console.log('Alert placed in dedicated alert container');
        } else if (activeTab.length > 0) {
            // Fallback: place after the first card header if available, otherwise prepend to tab
            const firstCardBody = activeTab.find('.card-body').first();
            if (firstCardBody.length > 0) {
                firstCardBody.prepend(alertHtml);
                console.log('Alert placed in first card body');
            } else {
                activeTab.prepend(alertHtml);
                console.log('Alert prepended to tab content');
            }
        } else {
            console.error('Target container not found!');
            // Final fallback to body if no container exists
            $('body').prepend(alertHtml);
        }

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            $('.alert').fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);

        // Make sure Bootstrap alert is properly initialized
        try {
            // For Bootstrap 5
            const alertNode = document.querySelector('.alert');
            if (alertNode) {
                const bsAlert = new bootstrap.Alert(alertNode);
            }
        } catch (e) {
            console.error('Error initializing Bootstrap alert:', e);
        }

        setTimeout(() => {
            try {
                $('.alert').alert('close');
            } catch (e) {
                console.error('Error closing alert:', e);
                // Fallback if alert('close') doesn't work
                $('.alert').fadeOut('slow', function() {
                    $(this).remove();
                });
            }
        }, 5000);
    }

    // Individual update for New Pins - Enhanced with Hybrid Duplicate Prevention
    $('.update-single-pin').on('click', function() {
        const postId = $(this).data('post-id');
        const pinUrlInput = $(`input[name="pin_link[${postId}]"]`);
        const formData = {
            form_type: 'add_pin',
            post_id: postId,
            pin_link: pinUrlInput.val().trim()
        };

        handlePinUpdate($(this), postId, pinUrlInput, formData, () => {
            updateDailyQuota(1);
        });
    });

    // Individual update for Re-Pins - Enhanced with Hybrid Duplicate Prevention
    $('.update-single-repin').on('click', function() {
        const postId = $(this).data('post-id');
        const pinUrlInput = $(`input[name="repin_override[${postId}]"]`);
        const formData = {
            form_type: 'add_pin',
            post_id: postId,
            pin_link: pinUrlInput.val().trim()
        };

        handlePinUpdate($(this), postId, pinUrlInput, formData, () => {
            updateDailyQuota(1);
        });
    });

    // Individual update for Bulk Update Section - Enhanced with Hybrid Duplicate Prevention
    $(document).on('click', '.btn-update-individual', function() {
        const postId = $(this).data('post-id');
        const status = $('select[name="update_status[' + postId + ']"]').val();
        const urlInput = $('input[name="bulk_pin_link[' + postId + ']"]');
        const dateInput = $('input[name="bulk_pin_date[' + postId + ']"]');

        const formData = {
            form_type: 'bulk_individual_update',
            post_id: postId,
            status: status,
            bulk_pin_link: urlInput.val().trim(),
            bulk_pin_date: dateInput.val().trim()
        };

        handleBulkPinUpdate($(this), postId, urlInput, dateInput, formData, () => {
            updateDailyQuota(1);
        });
    });

    // Open Pin Details Modal
    $('.btn-details').on('click', function() {
        const pinsData = $(this).data('pins');
        let detailsHtml = '<ul class="list-group">';
        if (pinsData && pinsData.length > 0) {
            pinsData.forEach(function(pin, index) {
                detailsHtml += `<li class="list-group-item">Pin ${index+1}: <a href="${pin.link}" target="_blank">${pin.link}</a> <span class="text-muted">(${pin.date})</span></li>`;
            });
        } else {
            detailsHtml += '<li class="list-group-item">No details available.</li>';
        }
        detailsHtml += '</ul>';
        $('#pinDetailsContent').html(detailsHtml);
        var myModal = new bootstrap.Modal(document.getElementById('pinDetailsModal'));
        myModal.show();
    });

    // Submission History Filtering
    // Set default filter to "all" to show all records by default
    $('#history-filter-select').val('all').trigger('change');

    $('#history-filter-select').on('change', function() {
        const filterValue = $(this).val();
        if (filterValue === 'custom') {
            $('#history-custom-range').show();
            // Initialize pure custom date range picker without preset ranges
            $('#history-custom-range').daterangepicker({
                autoUpdateInput: false,
                autoApply: false,
                showDropdowns: true,
                locale: {
                    format: 'YYYY-MM-DD',
                    cancelLabel: 'Clear',
                    applyLabel: 'Apply'
                }
            });

            $('#history-custom-range').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
                filterHistoryByDateRange(picker.startDate.format('YYYY-MM-DD'), picker.endDate.format('YYYY-MM-DD'));
            });

            $('#history-custom-range').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
                // Clear the date filter
                clearHistoryFilters();
            });
        } else {
            $('#history-custom-range').hide();

            if (filterValue === 'all') {
                // Clear all filters and show all records
                clearHistoryFilters();
                return;
            }

            let startDate, endDate;
            const today = moment().format('YYYY-MM-DD');
            if (filterValue === 'today') {
                startDate = endDate = today;
            } else if (filterValue === 'yesterday') {
                startDate = endDate = moment().subtract(1, 'days').format('YYYY-MM-DD');
            } else if (filterValue === 'last7') {
                // Use the same logic as analytics: -7 days to today (inclusive)
                startDate = moment().subtract(7, 'days').format('YYYY-MM-DD');
                endDate = today;
            } else if (filterValue === 'last15') {
                startDate = moment().subtract(15, 'days').format('YYYY-MM-DD');
                endDate = today;
            } else if (filterValue === 'last30') {
                startDate = moment().subtract(29, 'days').format('YYYY-MM-DD');
                endDate = today;
            } else if (filterValue === 'lastmonth') {
                startDate = moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD');
                endDate = moment().subtract(1, 'months').endOf('month').format('YYYY-MM-DD');
            } else if (filterValue === 'last3months') {
                startDate = moment().subtract(3, 'months').format('YYYY-MM-DD');
                endDate = today;
            } else if (filterValue === 'last6months') {
                startDate = moment().subtract(6, 'months').format('YYYY-MM-DD');
                endDate = today;
            } else if (filterValue === 'lastyear') {
                startDate = moment().subtract(1, 'year').format('YYYY-MM-DD');
                endDate = today;
            } else if (filterValue === 'thisquarter') {
                startDate = moment().startOf('quarter').format('YYYY-MM-DD');
                endDate = today;
            } else if (filterValue === 'lastquarter') {
                startDate = moment().subtract(1, 'quarter').startOf('quarter').format('YYYY-MM-DD');
                endDate = moment().subtract(1, 'quarter').endOf('quarter').format('YYYY-MM-DD');
            } else if (filterValue === 'thismonth') {
                startDate = moment().startOf('month').format('YYYY-MM-DD');
                endDate = today;
            }
            filterHistoryByDateRange(startDate, endDate);
        }
    });

    // Function to clear all history filters and reset table
    function clearHistoryFilters() {
        console.log('Clearing all history filters');

        // Remove all custom search functions
        $.fn.dataTable.ext.search = $.fn.dataTable.ext.search.filter(function(fn) {
            return fn.name !== 'dateRangeFilter';
        });

        // Clear any DataTable search
        historyTable.search('').draw();

        console.log('Filters cleared, showing all records');
    }

    function filterHistoryByDateRange(startDate, endDate) {
        console.log('Filtering by date range:', startDate, 'to', endDate);

        // First, completely clear any existing filters
        clearHistoryFilters();

        // Create new filter function
        function dateRangeFilter(settings, data, dataIndex) {
            if (settings.nTable.id !== 'historyTable') {
                return true;
            }

            // Get the row data using DataTable's API
            const rowData = historyTable.row(dataIndex).data();
            if (!rowData) {
                return false;
            }

            // Get the actual DOM row to access data-date attribute
            const rowNode = historyTable.row(dataIndex).node();
            const rowDate = $(rowNode).attr('data-date');

            if (!rowDate) {
                console.log('No data-date attribute found for row:', dataIndex);
                return false;
            }

            // Use simple string comparison like the analytics do
            const isInRange = rowDate >= startDate && rowDate <= endDate;

            if (dataIndex < 5) { // Log first few rows for debugging
                console.log('Row', dataIndex, '- Date:', rowDate, '- In range [', startDate, 'to', endDate, ']:', isInRange);
            }

            return isInRange;
        }
        dateRangeFilter.name = 'dateRangeFilter';

        // Add the new filter and redraw
        $.fn.dataTable.ext.search.push(dateRangeFilter);
        historyTable.draw();

        console.log('Filter applied, table redrawn');
    }

    // Export CSV from Submission History
    $('#export-csv').on('click', function() {
        let csv = 'Post Title,Pin Date,Pin Link\n';

        // Use DataTable API to get all filtered rows (not just visible ones)
        // This ensures we export all data that matches current filters, regardless of pagination
        const filteredRows = historyTable.rows({ search: 'applied' }).nodes();

        $(filteredRows).each(function() {
            const title = $(this).find('td:eq(1)').text().trim();
            const date = $(this).find('td:eq(2)').text().trim();
            const link = $(this).find('td:eq(3) a').attr('href') || '';
            csv += `"${title}","${date}","${link}"\n`;
        });

        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'submission_history.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    });

    // Clear Pin History for selected posts
    $('#bulk-clear-pins-btn').on('click', function() {
        var checkedBoxes = $('.bulk-check:checked');
        if (checkedBoxes.length === 0) {
            showAlert('warning', 'Please select at least one post to clear pin history.');
            return;
        }

        if (confirm(`Clear pin history for ${checkedBoxes.length} selected posts? This will set them to "Not Pinned" status.`)) {
            var postIds = [];
            checkedBoxes.each(function() {
                postIds.push($(this).val());
            });

            $.post(window.location.href, {
                form_type: 'clear_pin_history',
                post_ids: postIds
            }, function(response) {
                showAlert(response.success ? 'success' : 'danger', response.message);
                if (response.success) {
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                }
            }, 'json')
            .fail(function(jqXHR, textStatus, errorThrown) {
                console.error('AJAX request failed:', textStatus, errorThrown);
                showAlert('danger', 'Error: ' + textStatus + '. Please try again.');
            });
        }
    });

    // Manage Pin History - Use document delegation for dynamic elements
    $(document).on('click', '.btn-manage-history', function() {
        const postId = $(this).data('post-id');
        $('#pinHistoryContent').show();
        $('#pinHistoryEditor').hide();
        $('#pinHistoryTableBody').empty();

        // Show the modal
        var pinHistoryModal = new bootstrap.Modal(document.getElementById('pinHistoryModal'));
        pinHistoryModal.show();

        // Load pin history data
        $.post(window.location.href, {
            form_type: 'manage_pin_history',
            post_id: postId,
            action: 'get'
        }, function(response) {
            if (response.success) {
                const history = response.data;
                $('#pinHistoryModalLabel').text('Manage Pin History: ' + history.title);

                // Store post ID for later use
                $('#savePinHistory').data('post-id', postId);

                // Hide loading spinner and show editor
                $('#pinHistoryContent').hide();
                $('#pinHistoryEditor').show();

                // Populate table with pins
                if (history.pins && history.pins.length > 0) {
                    history.pins.forEach(function(pin, index) {
                        addPinHistoryRow(index + 1, pin.link, pin.date);
                    });
                } else {
                    // No pins, add an empty row
                    addPinHistoryRow(1, '', '');
                }
            } else {
                $('#pinHistoryContent').html('<div class="alert alert-danger">' + response.message + '</div>');
            }
        }, 'json')
        .fail(function(jqXHR, textStatus, errorThrown) {
            console.error('AJAX request failed:', textStatus, errorThrown);
            $('#pinHistoryContent').html('<div class="alert alert-danger">Error loading pin history. Please try again.</div>');
        });
    });

    // Add new pin row
    $('#addPinRow').on('click', function() {
        const rowCount = $('#pinHistoryTableBody tr').length;
        addPinHistoryRow(rowCount + 1, '', '');
    });

    // Function to add a row to the pin history table
    function addPinHistoryRow(number, link, date) {
        const row = `
            <tr>
                <td class="pin-number">${number}</td>
                <td>
                    <input type="url" class="form-control pin-url url-input-enhanced" value="${link}" placeholder="https://www.pinterest.com/pin/..." pattern="https://www.pinterest.com/pin/.*">
                </td>
                <td>
                    <input type="date" class="form-control pin-date date-input-enhanced" value="${date}">
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger remove-pin">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        $('#pinHistoryTableBody').append(row);

        // Initialize date picker for the new row
        flatpickr($('#pinHistoryTableBody tr:last-child .pin-date')[0], {
            dateFormat: 'Y-m-d',
            allowInput: true
        });
    }

    // Remove pin row
    $(document).on('click', '.remove-pin', function() {
        $(this).closest('tr').remove();
        // Renumber rows
        $('#pinHistoryTableBody tr').each(function(index) {
            $(this).find('.pin-number').text(index + 1);
        });
    });

    // Save pin history changes
    $('#savePinHistory').on('click', function() {
        const postId = $(this).data('post-id');
        const pins = [];

        // Validate inputs
        let isValid = true;
        $('#pinHistoryTableBody tr').each(function() {
            const url = $(this).find('.pin-url').val().trim();
            const date = $(this).find('.pin-date').val().trim();

            if (url && !url.startsWith('https://www.pinterest.com/pin/')) {
                showAlert('warning', 'Please enter valid Pinterest URLs (must start with https://www.pinterest.com/pin/)');
                $(this).find('.pin-url').addClass('is-invalid');
                isValid = false;
                return false;
            }

            if (url && (!date || isNaN(Date.parse(date)))) {
                showAlert('warning', 'Please enter valid dates for all pins');
                $(this).find('.pin-date').addClass('is-invalid');
                isValid = false;
                return false;
            }

            if (url && date) {
                pins.push({
                    link: url,
                    date: date
                });
            }
        });

        if (!isValid) return;

        // Send data to server
        $.post(window.location.href, {
            form_type: 'manage_pin_history',
            post_id: postId,
            pins: JSON.stringify(pins)
        }, function(response) {
            showAlert(response.success ? 'success' : 'danger', response.message);
            if (response.success) {
                var pinHistoryModal = bootstrap.Modal.getInstance(document.getElementById('pinHistoryModal'));
                pinHistoryModal.hide();
                setTimeout(function() {
                    location.reload();
                }, 1500);
            }
        }, 'json')
        .fail(function(jqXHR, textStatus, errorThrown) {
            console.error('AJAX request failed:', textStatus, errorThrown);
            showAlert('danger', 'Error: ' + textStatus + '. Please try again.');
        });
    });

    // Enhanced functionality for new features

    // Quick action cards in Bulk Update tab
    $('#bulk-select-all-action').on('click', function() {
        var rows = bulkTable.rows({ 'search': 'applied' }).nodes();
        $('input.bulk-check', rows).prop('checked', true);
        updateBulkActionState();
    });

    $('#clear-all-filters').on('click', function() {
        if (typeof bulkTable !== 'undefined') {
            // Remove custom search functions
            $.fn.dataTable.ext.search = $.fn.dataTable.ext.search.filter(function(fn) {
                return fn.name !== 'statusFilter';
            });
            bulkTable.search('').columns().search('').draw();
        }
        $('#filter-status').hide();
        $('.clickable-stat-card').removeClass('active');
        $('#date-range-filter').val('');
    });

    // Filter functionality for New Pins tab
    $('.filter-new-pins').on('click', function(e) {
        e.preventDefault();
        const filter = $(this).data('filter');
        const items = $('.new-pins-list .task-item');

        items.show(); // Show all first

        if (filter === 'today') {
            items.each(function() {
                const daysOld = parseInt($(this).find('.status-badge:contains("days old")').text());
                if (daysOld !== 0) $(this).hide();
            });
        } else if (filter === 'week') {
            items.each(function() {
                const daysOld = parseInt($(this).find('.status-badge:contains("days old")').text());
                if (daysOld > 7) $(this).hide();
            });
        } else if (filter === 'month') {
            items.each(function() {
                const daysOld = parseInt($(this).find('.status-badge:contains("days old")').text());
                if (daysOld > 30) $(this).hide();
            });
        }

        // Update items count
        const visibleCount = items.filter(':visible').length;
        $('.new-pins-list').siblings('.items-count').find('.fw-medium').text(visibleCount);
    });

    // Sort functionality for Re-Pins tab
    $('.sort-repins').on('click', function(e) {
        e.preventDefault();
        const sortType = $(this).data('sort');
        const container = $('.repin-tasks-list');
        const items = container.find('.task-item').get();

        items.sort(function(a, b) {
            if (sortType === 'urgent') {
                const aDays = parseInt($(a).find('.status-badge:contains("days since")').text());
                const bDays = parseInt($(b).find('.status-badge:contains("days since")').text());
                return bDays - aDays; // Descending
            } else if (sortType === 'progress') {
                const aProgress = $(a).find('.pin-indicator.completed').length;
                const bProgress = $(b).find('.pin-indicator.completed').length;
                return aProgress - bProgress; // Ascending
            } else if (sortType === 'date') {
                const aDays = parseInt($(a).find('.status-badge:contains("days since")').text());
                const bDays = parseInt($(b).find('.status-badge:contains("days since")').text());
                return bDays - aDays; // Descending
            }
            return 0; // Default order
        });

        $.each(items, function(index, item) {
            container.append(item);
        });
    });

    // Enhanced file upload for Import tab
    const fileUploadArea = $('#file-upload-area');
    const fileInput = $('#csv_file');
    const fileInfo = $('#file-info');
    const importBtn = $('#import-btn');

    // Drag and drop functionality
    fileUploadArea.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    fileUploadArea.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    fileUploadArea.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');

        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            fileInput[0].files = files;
            handleFileSelection(files[0]);
        }
    });

    // File input change
    fileInput.on('change', function() {
        if (this.files.length > 0) {
            handleFileSelection(this.files[0]);
        }
    });

    function handleFileSelection(file) {
        if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
            showAlert('warning', 'Please select a CSV file.');
            return;
        }

        // Show file info
        $('#file-name').text(file.name);
        $('#file-size').text(formatFileSize(file.size));
        fileInfo.show();
        importBtn.prop('disabled', false);

        // Update step progress
        $('#step-1').addClass('completed').removeClass('active');
        $('#step-2').addClass('active');
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Click to upload
    fileUploadArea.on('click', function() {
        fileInput.click();
    });

    // Email Report Functionality
    $('#send-email-btn').on('click', function(e) {
        e.preventDefault();
        console.log('Email button clicked');

        const $btn = $(this);
        const $btnText = $btn.find('.btn-text');
        const $alert = $('#email-alert');

        console.log('Button elements found:', {
            btn: $btn.length,
            btnText: $btnText.length,
            alert: $alert.length
        });

        // Disable button and show loading state
        $btn.prop('disabled', true);
        $btnText.text('Sending...');
        $btn.find('i').removeClass('fa-paper-plane').addClass('fa-spinner fa-spin');

        // Hide any previous alerts
        $alert.hide().removeClass('alert-success alert-danger alert-info');

        // Send AJAX request
        console.log('Sending AJAX request to:', window.location.href);
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                form_type: 'send_email_report'
            },
            dataType: 'json',
            timeout: 30000, // 30 second timeout
            beforeSend: function() {
                console.log('AJAX request started');
            },
            success: function(response) {
                console.log('AJAX success response:', response);
                if (response.success) {
                    // Success
                    $alert.addClass('alert-success')
                          .html('<i class="fas fa-check-circle me-2"></i>' + response.message)
                          .show();

                    // Update last sent time
                    $('#last-sent-time').text('Just now');

                    // Show additional stats if available
                    if (response.total_submissions !== undefined) {
                        const statsHtml = `
                            <div class="mt-2 small">
                                <strong>Report Summary:</strong>
                                ${response.total_submissions} total pins
                                (${response.new_pins} new, ${response.re_pins} re-pins),
                                ${response.not_pinned} posts not pinned
                            </div>
                        `;
                        $alert.append(statsHtml);
                    }
                } else {
                    // Error
                    $alert.addClass('alert-danger')
                          .html('<i class="fas fa-exclamation-triangle me-2"></i>' + response.message)
                          .show();
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX error:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText,
                    responseJSON: xhr.responseJSON
                });

                let errorMessage = 'Failed to send email report.';

                if (status === 'timeout') {
                    errorMessage = 'Request timed out. The email may still be processing.';
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (error) {
                    errorMessage += ' Error: ' + error;
                }

                $alert.addClass('alert-danger')
                      .html('<i class="fas fa-exclamation-triangle me-2"></i>' + errorMessage)
                      .show();
            },
            complete: function() {
                // Re-enable button and restore original state
                $btn.prop('disabled', false);
                $btnText.text('Send Report');
                $btn.find('i').removeClass('fa-spinner fa-spin').addClass('fa-paper-plane');

                // Auto-hide alert after 10 seconds
                setTimeout(function() {
                    $alert.fadeOut();
                }, 10000);
            }
        });
    });

    // ===== IGNORED POSTS FUNCTIONALITY =====

    // Initialize DataTable for Ignored Posts (if table exists)
    var ignoredPostsTable;
    if ($('#ignoredPostsTable').length > 0) {
        ignoredPostsTable = $('#ignoredPostsTable').DataTable({
            order: [[3, "desc"]], // Sort by ignored date descending
            pageLength: 25,
            dom: "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
                 "<'row'<'col-sm-12'tr>>" +
                 "<'row align-items-center'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search ignored posts...",
                lengthMenu: "Show _MENU_ posts",
                info: "Showing _START_ to _END_ of _TOTAL_ ignored posts",
                paginate: {
                    next: '<i class="fas fa-chevron-right"></i>',
                    previous: '<i class="fas fa-chevron-left"></i>'
                }
            },
            columnDefs: [
                { orderable: false, targets: [0, 4] }, // Disable sorting for checkbox and action columns
                { className: "align-middle", targets: "_all" }
            ]
        });
    }

    // Select All functionality for ignored posts
    $('#select-all-ignored').on('change', function() {
        if (typeof ignoredPostsTable !== 'undefined') {
            var rows = ignoredPostsTable.rows({ 'search': 'applied' }).nodes();
            $('input.ignored-post-check', rows).prop('checked', this.checked);
        } else {
            $('.ignored-post-check').prop('checked', this.checked);
        }
        updateIgnoredPostsActionState();
    });

    // Individual checkbox change handler
    $(document).on('change', '.ignored-post-check', function() {
        updateIgnoredPostsActionState();
        if (!$(this).prop('checked')) {
            $('#select-all-ignored').prop('checked', false);
        }
    });

    // Update action button state based on selection
    function updateIgnoredPostsActionState() {
        var checkedCount = $('.ignored-post-check:checked').length;
        var hasSelection = checkedCount > 0;

        // Show/hide and enable/disable restore selected button
        $('#restore-selected-btn').toggle(hasSelection);
        $('#restore-selected-btn').prop('disabled', !hasSelection);

        // Update select all checkbox state
        if (typeof ignoredPostsTable !== 'undefined') {
            var visibleRows = ignoredPostsTable.rows({ 'search': 'applied' }).nodes();
            var visibleChecked = $('.ignored-post-check:checked', visibleRows).length;
            var allVisible = visibleRows.length;
            $('#select-all-ignored').prop('checked', visibleChecked === allVisible && allVisible > 0);
        } else {
            var totalCheckboxes = $('.ignored-post-check').length;
            var checkedCheckboxes = $('.ignored-post-check:checked').length;
            $('#select-all-ignored').prop('checked', checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0);
        }
    }

    // Quick action cards functionality
    $('#select-all-ignored-action').on('click', function() {
        if (typeof ignoredPostsTable !== 'undefined') {
            var rows = ignoredPostsTable.rows({ 'search': 'applied' }).nodes();
            $('input.ignored-post-check', rows).prop('checked', true);
        } else {
            $('.ignored-post-check').prop('checked', true);
        }
        updateIgnoredPostsActionState();
    });

    $('#clear-ignored-selection').on('click', function() {
        $('.ignored-post-check').prop('checked', false);
        $('#select-all-ignored').prop('checked', false);
        updateIgnoredPostsActionState();
    });

    $('#restore-all-ignored-action').on('click', function() {
        if (confirm('Are you sure you want to restore all ignored posts? This action cannot be undone.')) {
            // Submit the restore all form
            $('form input[name="form_type"][value="restore_all_ignored_posts"]').closest('form').submit();
        }
    });

    $('#search-ignored-posts').on('click', function() {
        if (typeof ignoredPostsTable !== 'undefined') {
            // Focus on the search input
            $('.dataTables_filter input').focus();
        } else {
            // If no DataTable, show alert
            showAlert('info', 'Search functionality requires DataTable. Please refresh the page.');
        }
    });

    // Restore selected posts functionality
    $('#restore-selected-btn').on('click', function() {
        var checkedBoxes = $('.ignored-post-check:checked');
        if (checkedBoxes.length === 0) {
            showAlert('warning', 'Please select at least one post to restore.');
            return;
        }

        if (confirm(`Restore ${checkedBoxes.length} selected post(s)? This will make them available for pinning again.`)) {
            // Create a form to submit the bulk restore
            var form = $('<form method="post" style="display: none;"></form>');
            form.append('<input type="hidden" name="form_type" value="bulk_restore_ignored_posts">');

            checkedBoxes.each(function() {
                var postId = $(this).val();
                form.append('<input type="hidden" name="post_ids[]" value="' + postId + '">');
            });

            $('body').append(form);
            form.submit();
        }
    });

    // Initialize ignored posts action state on page load
    if ($('.ignored-post-check').length > 0) {
        updateIgnoredPostsActionState();
    }
});
</script>

</body>
</html>
