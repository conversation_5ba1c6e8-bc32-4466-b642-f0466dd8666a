<?php

namespace App\Models;

/**
 * IgnoredPost Model
 * 
 * Represents the wp_ignored_posts table
 * Stores posts that should be ignored from pin suggestions
 */
class IgnoredPost extends BaseModel
{
    protected $table = 'ignored_posts';
    protected $primaryKey = 'post_id';
    
    protected $fillable = [
        'post_id',
        'ignored_date'
    ];

    /**
     * Check if a post is ignored
     */
    public static function isPostIgnored($postId)
    {
        $ignored = static::find($postId);
        return $ignored !== null;
    }

    /**
     * Ignore a post
     */
    public static function ignorePost($postId, $ignoredDate = null)
    {
        if (static::isPostIgnored($postId)) {
            return static::find($postId);
        }
        
        return static::create([
            'post_id' => $postId,
            'ignored_date' => $ignoredDate ?: date('Y-m-d')
        ]);
    }

    /**
     * Unignore a post
     */
    public static function unignorePost($postId)
    {
        $ignored = static::find($postId);
        if ($ignored) {
            return $ignored->delete();
        }
        return false;
    }

    /**
     * Get all ignored post IDs
     */
    public static function getAllIgnoredPostIds()
    {
        $instance = new static();
        $sql = "SELECT `post_id` FROM `{$instance->getTable()}`";
        
        $result = static::getConnection()->query($sql);
        $postIds = [];
        
        while ($row = $result->fetch_assoc()) {
            $postIds[] = (int)$row['post_id'];
        }
        
        return $postIds;
    }

    /**
     * Get ignored posts with details
     */
    public static function getIgnoredWithDetails($limit = 50)
    {
        $instance = new static();
        $ignoredTable = $instance->getTable();
        
        // Get WordPress posts table name
        $wpPostsTable = 'wp_posts'; // Default WordPress posts table
        
        $sql = "SELECT i.post_id, i.ignored_date, p.post_title, p.post_date, p.post_status 
                FROM `{$ignoredTable}` i 
                LEFT JOIN `{$wpPostsTable}` p ON i.post_id = p.ID 
                ORDER BY i.ignored_date DESC 
                LIMIT ?";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('i', $limit);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $ignored = [];
        while ($row = $result->fetch_assoc()) {
            $ignored[] = [
                'post_id' => (int)$row['post_id'],
                'ignored_date' => $row['ignored_date'],
                'post_title' => $row['post_title'],
                'post_date' => $row['post_date'],
                'post_status' => $row['post_status']
            ];
        }
        
        $stmt->close();
        return $ignored;
    }

    /**
     * Get recently ignored posts
     */
    public static function getRecentlyIgnored($days = 30)
    {
        $cutoffDate = date('Y-m-d', strtotime("-{$days} days"));
        $instance = new static();
        
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `ignored_date` >= ? ORDER BY `ignored_date` DESC";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('s', $cutoffDate);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $models = [];
        while ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }
        
        $stmt->close();
        return $models;
    }

    /**
     * Get ignored posts statistics
     */
    public static function getStatistics()
    {
        $instance = new static();
        $table = $instance->getTable();
        
        // Total ignored
        $totalResult = static::getConnection()->query("SELECT COUNT(*) as total FROM `{$table}`");
        $total = $totalResult->fetch_assoc()['total'];
        
        // Today's ignored
        $today = date('Y-m-d');
        $todayResult = static::getConnection()->query("SELECT COUNT(*) as today FROM `{$table}` WHERE `ignored_date` = '{$today}'");
        $todayCount = $todayResult->fetch_assoc()['today'];
        
        // This week's ignored
        $weekStart = date('Y-m-d', strtotime('monday this week'));
        $weekResult = static::getConnection()->query("SELECT COUNT(*) as week FROM `{$table}` WHERE `ignored_date` >= '{$weekStart}'");
        $weekCount = $weekResult->fetch_assoc()['week'];
        
        // This month's ignored
        $monthStart = date('Y-m-01');
        $monthResult = static::getConnection()->query("SELECT COUNT(*) as month FROM `{$table}` WHERE `ignored_date` >= '{$monthStart}'");
        $monthCount = $monthResult->fetch_assoc()['month'];
        
        return [
            'total' => (int)$total,
            'today' => (int)$todayCount,
            'week' => (int)$weekCount,
            'month' => (int)$monthCount
        ];
    }

    /**
     * Clean up old ignored posts
     */
    public static function cleanupOldIgnored($daysOld = 365)
    {
        $cutoffDate = date('Y-m-d', strtotime("-{$daysOld} days"));
        $instance = new static();
        
        $sql = "DELETE FROM `{$instance->getTable()}` WHERE `ignored_date` < ?";
        $stmt = static::getConnection()->prepare($sql);
        
        if (!$stmt) {
            return 0;
        }
        
        $stmt->bind_param('s', $cutoffDate);
        $stmt->execute();
        $affectedRows = static::getConnection()->affectedRows();
        $stmt->close();
        
        return $affectedRows;
    }

    /**
     * Bulk ignore posts
     */
    public static function bulkIgnorePosts(array $postIds, $ignoredDate = null)
    {
        if (empty($postIds)) {
            return 0;
        }
        
        $ignoredDate = $ignoredDate ?: date('Y-m-d');
        $instance = new static();
        $table = $instance->getTable();
        
        // Prepare bulk insert
        $placeholders = str_repeat('(?,?),', count($postIds) - 1) . '(?,?)';
        $sql = "INSERT IGNORE INTO `{$table}` (`post_id`, `ignored_date`) VALUES {$placeholders}";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return 0;
        }
        
        // Prepare values array
        $values = [];
        $types = '';
        foreach ($postIds as $postId) {
            $values[] = $postId;
            $values[] = $ignoredDate;
            $types .= 'is';
        }
        
        $stmt->bind_param($types, ...$values);
        $stmt->execute();
        $affectedRows = static::getConnection()->affectedRows();
        $stmt->close();
        
        return $affectedRows;
    }

    /**
     * Get ignored posts by date range
     */
    public static function getByDateRange($startDate, $endDate)
    {
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `ignored_date` BETWEEN ? AND ? ORDER BY `ignored_date` DESC";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('ss', $startDate, $endDate);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $models = [];
        while ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }
        
        $stmt->close();
        return $models;
    }
}
