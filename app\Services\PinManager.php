<?php

use App\Models\PinHistory;
use App\Models\PinDetail;
use App\Models\PendingPin;
use App\Models\IgnoredPost;

/**
 * Pin Manager Service
 * 
 * Core CRUD operations for pin management
 */
class PinManager
{
    /**
     * Create a new pin record
     */
    public static function createPin($postId, $title, $url, $pinLink = null, $publishDate = null, $pinDate = null)
    {
        try {
            // Create or update pin history
            $pinHistory = PinHistory::createOrUpdate($postId, $title, $url, $publishDate);
            
            // Create pin detail if pin link provided
            if ($pinLink) {
                $pinDetail = PinDetail::create([
                    'history_id' => $pinHistory->id,
                    'pin_link' => $pinLink,
                    'pin_date' => $pinDate ?: date('Y-m-d')
                ]);
                
                return [
                    'success' => true,
                    'pin_history' => $pinHistory,
                    'pin_detail' => $pinDetail,
                    'message' => 'Pin created successfully'
                ];
            }
            
            return [
                'success' => true,
                'pin_history' => $pinHistory,
                'message' => 'Pin history created successfully'
            ];
            
        } catch (Exception $e) {
            error_log('Pin Manager Error - Create Pin: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get pin by post ID
     */
    public static function getPinByPostId($postId)
    {
        try {
            $pinHistory = PinHistory::findByPostId($postId);
            if (!$pinHistory) {
                return [
                    'success' => false,
                    'error' => 'Pin not found'
                ];
            }
            
            $pinDetails = PinDetail::findByHistoryId($pinHistory->id);
            
            return [
                'success' => true,
                'pin_history' => $pinHistory,
                'pin_details' => $pinDetails,
                'total_pins' => count($pinDetails)
            ];
            
        } catch (Exception $e) {
            error_log('Pin Manager Error - Get Pin: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update pin information
     */
    public static function updatePin($postId, $data)
    {
        try {
            $pinHistory = PinHistory::findByPostId($postId);
            if (!$pinHistory) {
                return [
                    'success' => false,
                    'error' => 'Pin not found'
                ];
            }
            
            // Update pin history fields
            if (isset($data['title'])) {
                $pinHistory->title = $data['title'];
            }
            if (isset($data['url'])) {
                $pinHistory->url = $data['url'];
            }
            if (isset($data['publish_date'])) {
                $pinHistory->publish_date = $data['publish_date'];
            }
            
            $pinHistory->save();
            
            return [
                'success' => true,
                'pin_history' => $pinHistory,
                'message' => 'Pin updated successfully'
            ];
            
        } catch (Exception $e) {
            error_log('Pin Manager Error - Update Pin: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete pin and all its details
     */
    public static function deletePin($postId)
    {
        try {
            $pinHistory = PinHistory::findByPostId($postId);
            if (!$pinHistory) {
                return [
                    'success' => false,
                    'error' => 'Pin not found'
                ];
            }
            
            // Delete all pin details first
            $pinDetails = PinDetail::findByHistoryId($pinHistory->id);
            foreach ($pinDetails as $detail) {
                $detail->delete();
            }
            
            // Delete pin history
            $pinHistory->delete();
            
            return [
                'success' => true,
                'message' => 'Pin deleted successfully'
            ];
            
        } catch (Exception $e) {
            error_log('Pin Manager Error - Delete Pin: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Add a new pin detail to existing pin history
     */
    public static function addPinDetail($postId, $pinLink, $pinDate = null)
    {
        try {
            $pinHistory = PinHistory::findByPostId($postId);
            if (!$pinHistory) {
                return [
                    'success' => false,
                    'error' => 'Pin history not found'
                ];
            }
            
            $pinDetail = PinDetail::create([
                'history_id' => $pinHistory->id,
                'pin_link' => $pinLink,
                'pin_date' => $pinDate ?: date('Y-m-d')
            ]);
            
            return [
                'success' => true,
                'pin_detail' => $pinDetail,
                'message' => 'Pin detail added successfully'
            ];
            
        } catch (Exception $e) {
            error_log('Pin Manager Error - Add Pin Detail: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get all pins with pagination
     */
    public static function getAllPins($page = 1, $perPage = 20)
    {
        try {
            $offset = ($page - 1) * $perPage;
            $histories = PinHistory::all();
            
            // Simple pagination (in a real implementation, you'd want to do this in the database)
            $total = count($histories);
            $paginatedHistories = array_slice($histories, $offset, $perPage);
            
            $pins = [];
            foreach ($paginatedHistories as $history) {
                $details = PinDetail::findByHistoryId($history->id);
                $pins[] = [
                    'history' => $history,
                    'details' => $details,
                    'total_pins' => count($details)
                ];
            }
            
            return [
                'success' => true,
                'pins' => $pins,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'total_pages' => ceil($total / $perPage)
                ]
            ];
            
        } catch (Exception $e) {
            error_log('Pin Manager Error - Get All Pins: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Search pins by title or URL
     */
    public static function searchPins($query, $limit = 20)
    {
        try {
            $histories = PinHistory::all();
            $results = [];
            
            foreach ($histories as $history) {
                if (stripos($history->title, $query) !== false || 
                    stripos($history->url, $query) !== false) {
                    $details = PinDetail::findByHistoryId($history->id);
                    $results[] = [
                        'history' => $history,
                        'details' => $details,
                        'total_pins' => count($details)
                    ];
                    
                    if (count($results) >= $limit) {
                        break;
                    }
                }
            }
            
            return [
                'success' => true,
                'results' => $results,
                'total_found' => count($results)
            ];
            
        } catch (Exception $e) {
            error_log('Pin Manager Error - Search Pins: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get pin statistics
     */
    public static function getPinStatistics()
    {
        try {
            $historyStats = PinHistory::getStatistics();
            $pendingStats = PendingPin::getStatistics();
            $ignoredStats = IgnoredPost::getStatistics();
            
            return [
                'success' => true,
                'statistics' => [
                    'pin_history' => $historyStats,
                    'pending_pins' => $pendingStats,
                    'ignored_posts' => $ignoredStats
                ]
            ];
            
        } catch (Exception $e) {
            error_log('Pin Manager Error - Get Statistics: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Bulk operations for pins
     */
    public static function bulkIgnorePosts(array $postIds)
    {
        try {
            $ignoredCount = IgnoredPost::bulkIgnorePosts($postIds);
            
            return [
                'success' => true,
                'ignored_count' => $ignoredCount,
                'message' => "Successfully ignored {$ignoredCount} posts"
            ];
            
        } catch (Exception $e) {
            error_log('Pin Manager Error - Bulk Ignore: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Clean up old data
     */
    public static function cleanupOldData($daysOld = 365)
    {
        try {
            $expiredPins = PinHistory::deleteExpiredPins($daysOld);
            $oldPending = PendingPin::cleanupOldPending($daysOld);
            $oldIgnored = IgnoredPost::cleanupOldIgnored($daysOld);
            
            return [
                'success' => true,
                'cleanup_results' => [
                    'expired_pins' => $expiredPins,
                    'old_pending' => $oldPending,
                    'old_ignored' => $oldIgnored
                ],
                'message' => 'Cleanup completed successfully'
            ];
            
        } catch (Exception $e) {
            error_log('Pin Manager Error - Cleanup: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
