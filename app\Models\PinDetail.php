<?php

namespace App\Models;

/**
 * PinDetail Model
 * 
 * Represents the wp_pin_details table
 * Stores detailed information about individual pins
 */
class PinDetail extends BaseModel
{
    protected $table = 'pin_details';
    
    protected $fillable = [
        'history_id',
        'pin_link',
        'pin_date'
    ];

    /**
     * Get the pin history record this detail belongs to
     */
    public function pinHistory()
    {
        return PinHistory::find($this->history_id);
    }

    /**
     * Find details by history ID
     */
    public static function findByHistoryId($historyId)
    {
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `history_id` = ?";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('i', $historyId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $models = [];
        while ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }
        
        $stmt->close();
        return $models;
    }

    /**
     * Get all pin details for a specific post ID
     */
    public static function findByPostId($postId)
    {
        $pinHistory = PinHistory::findByPostId($postId);
        if (!$pinHistory) {
            return [];
        }
        
        return static::findByHistoryId($pinHistory->id);
    }

    /**
     * Create pin detail record
     */
    public static function createForPost($postId, $pinLink, $pinDate = null)
    {
        $pinHistory = PinHistory::findByPostId($postId);
        if (!$pinHistory) {
            return null;
        }
        
        return static::create([
            'history_id' => $pinHistory->id,
            'pin_link' => $pinLink,
            'pin_date' => $pinDate ?: date('Y-m-d')
        ]);
    }

    /**
     * Get pin details with history information
     */
    public static function getWithHistory($limit = 50)
    {
        $instance = new static();
        $detailsTable = $instance->getTable();
        $historyTable = (new PinHistory())->getTable();
        
        $sql = "SELECT d.*, h.post_id, h.title, h.url, h.publish_date 
                FROM `{$detailsTable}` d 
                LEFT JOIN `{$historyTable}` h ON d.history_id = h.id 
                ORDER BY d.pin_date DESC, d.id DESC 
                LIMIT ?";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('i', $limit);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $details = [];
        while ($row = $result->fetch_assoc()) {
            $details[] = $row;
        }
        
        $stmt->close();
        return $details;
    }

    /**
     * Get pin statistics by date range
     */
    public static function getStatisticsByDateRange($startDate, $endDate)
    {
        $instance = new static();
        $sql = "SELECT DATE(pin_date) as date, COUNT(*) as count 
                FROM `{$instance->getTable()}` 
                WHERE `pin_date` BETWEEN ? AND ? 
                GROUP BY DATE(pin_date) 
                ORDER BY date ASC";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('ss', $startDate, $endDate);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $statistics = [];
        while ($row = $result->fetch_assoc()) {
            $statistics[] = [
                'date' => $row['date'],
                'count' => (int)$row['count']
            ];
        }
        
        $stmt->close();
        return $statistics;
    }

    /**
     * Get today's pin details
     */
    public static function getTodaysPins()
    {
        $today = date('Y-m-d');
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE DATE(`pin_date`) = ? ORDER BY `pin_date` DESC";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('s', $today);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $models = [];
        while ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }
        
        $stmt->close();
        return $models;
    }

    /**
     * Delete all details for a history record
     */
    public static function deleteByHistoryId($historyId)
    {
        $instance = new static();
        $sql = "DELETE FROM `{$instance->getTable()}` WHERE `history_id` = ?";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return false;
        }
        
        $stmt->bind_param('i', $historyId);
        $result = $stmt->execute();
        $stmt->close();
        
        return $result;
    }

    /**
     * Get pin count for a specific history record
     */
    public static function getCountByHistoryId($historyId)
    {
        $instance = new static();
        $sql = "SELECT COUNT(*) as count FROM `{$instance->getTable()}` WHERE `history_id` = ?";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return 0;
        }
        
        $stmt->bind_param('i', $historyId);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stmt->close();
        
        return (int)$row['count'];
    }

    /**
     * Get latest pin detail for a history record
     */
    public static function getLatestByHistoryId($historyId)
    {
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `history_id` = ? ORDER BY `pin_date` DESC, `id` DESC LIMIT 1";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return null;
        }
        
        $stmt->bind_param('i', $historyId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $stmt->close();
            return $model;
        }
        
        $stmt->close();
        return null;
    }
}
