<?php

/**
 * Create Quota Rules Table Migration
 * 
 * Creates the wp_quota_rules table for advanced quota management
 */

function create_quota_rules_table() {
    $db = pinboard_db();
    if (!$db) {
        throw new Exception('Database connection failed');
    }
    
    $table_name = 'wp_quota_rules';
    
    $sql = "CREATE TABLE IF NOT EXISTS `{$table_name}` (
        id bigint(20) AUTO_INCREMENT,
        rule_name varchar(255) NOT NULL,
        rule_type varchar(50) NOT NULL,
        condition_field varchar(100) NOT NULL,
        condition_operator varchar(20) NOT NULL,
        condition_value JSON,
        quota_adjustment int NOT NULL,
        priority int DEFAULT 10,
        is_active boolean DEFAULT true,
        description text,
        created_at timestamp DEFAULT CURRENT_TIMESTAMP,
        updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY rule_type (rule_type),
        KEY is_active (is_active),
        KEY priority (priority)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    
    if (!$db->query($sql)) {
        throw new Exception("Error creating quota rules table: " . $db->error);
    }
    
    return true;
}

/**
 * Drop quota rules table
 */
function drop_quota_rules_table() {
    $db = pinboard_db();
    if (!$db) {
        throw new Exception('Database connection failed');
    }
    
    $table_name = 'wp_quota_rules';
    $sql = "DROP TABLE IF EXISTS `{$table_name}`";
    
    if (!$db->query($sql)) {
        throw new Exception("Error dropping quota rules table: " . $db->error);
    }
    
    return true;
}

/**
 * Check if quota rules table exists
 */
function quota_rules_table_exists() {
    $db = pinboard_db();
    if (!$db) {
        return false;
    }
    
    $table_name = 'wp_quota_rules';
    $sql = "SHOW TABLES LIKE '{$table_name}'";
    $result = $db->query($sql);
    
    return $result && $result->num_rows > 0;
}

/**
 * Run the migration
 */
function run_quota_rules_migration() {
    try {
        if (!quota_rules_table_exists()) {
            create_quota_rules_table();
            echo "Quota rules table created successfully.\n";
            
            // Initialize default rules
            require_once dirname(__DIR__, 2) . '/bootstrap/app.php';
            use App\Models\QuotaRule;
            QuotaRule::createDefaultRules();
            echo "Default quota rules initialized.\n";
            
            return true;
        } else {
            echo "Quota rules table already exists.\n";
            return true;
        }
    } catch (Exception $e) {
        echo "Migration failed: " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * Rollback the migration
 */
function rollback_quota_rules_migration() {
    try {
        if (quota_rules_table_exists()) {
            drop_quota_rules_table();
            echo "Quota rules table dropped successfully.\n";
            return true;
        } else {
            echo "Quota rules table does not exist.\n";
            return true;
        }
    } catch (Exception $e) {
        echo "Rollback failed: " . $e->getMessage() . "\n";
        return false;
    }
}

// Run migration if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    require_once dirname(__DIR__, 2) . '/bootstrap/app.php';
    
    $action = $argv[1] ?? 'migrate';
    
    switch ($action) {
        case 'migrate':
            run_quota_rules_migration();
            break;
        case 'rollback':
            rollback_quota_rules_migration();
            break;
        default:
            echo "Usage: php create_quota_rules_table.php [migrate|rollback]\n";
            break;
    }
}
