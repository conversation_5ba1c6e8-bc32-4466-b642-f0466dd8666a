# Project Structure for Pin Board v10 Modernization

## Root Directory Structure

```
Pin_Board_v10/
├── app/                          # Laravel application core
│   ├── Console/                  # Artisan commands
│   │   ├── Commands/            # Custom commands
│   │   └── Kernel.php           # Command scheduler
│   ├── Http/                    # HTTP layer
│   │   ├── Controllers/         # Application controllers
│   │   │   ├── Api/            # API controllers for Chrome extension
│   │   │   ├── Dashboard/      # Dashboard controllers
│   │   │   └── Auth/           # Authentication controllers
│   │   ├── Middleware/         # Custom middleware
│   │   ├── Requests/           # Form request validation
│   │   └── Resources/          # API resources and transformers
│   ├── Models/                  # Eloquent models
│   │   ├── Pin/                # Pin-related models
│   │   ├── User/               # User-related models
│   │   └── WordPress/          # WordPress integration models
│   ├── Services/               # Business logic services
│   │   ├── PinService.php      # Pin management service
│   │   ├── QuotaService.php    # Quota calculation service
│   │   ├── EmailService.php    # Email reporting service
│   │   └── CsvService.php      # CSV import/export service
│   ├── Jobs/                   # Queue jobs
│   │   ├── ProcessPinJob.php   # Pin processing job
│   │   ├── SendEmailJob.php    # Email sending job
│   │   └── ResetExpiredPins.php # Pin reset job
│   └── Providers/              # Service providers
├── bootstrap/                   # Application bootstrap
├── config/                     # Configuration files
│   ├── app.php                 # Application configuration
│   ├── database.php            # Database configuration
│   ├── mail.php                # Email configuration
│   └── pinboard.php            # Custom pin board configuration
├── database/                   # Database files
│   ├── migrations/             # Database migrations
│   ├── seeders/               # Database seeders
│   └── factories/             # Model factories for testing
├── public/                     # Public web directory
│   ├── index.php              # Application entry point
│   ├── assets/                # Compiled assets
│   │   ├── css/               # Compiled CSS files
│   │   ├── js/                # Compiled JavaScript files
│   │   └── images/            # Image assets
│   └── api/                   # API entry points (for backward compatibility)
├── resources/                  # Raw assets and views
│   ├── views/                 # Blade templates
│   │   ├── dashboard/         # Dashboard views
│   │   ├── auth/              # Authentication views
│   │   ├── emails/            # Email templates
│   │   └── layouts/           # Layout templates
│   ├── js/                    # Vue.js components and JavaScript
│   │   ├── components/        # Vue components
│   │   │   ├── Dashboard/     # Dashboard components
│   │   │   ├── Pin/           # Pin management components
│   │   │   └── Common/        # Shared components
│   │   ├── pages/             # Vue page components
│   │   ├── services/          # Frontend services
│   │   └── app.js             # Main JavaScript entry point
│   ├── css/                   # SCSS/CSS source files
│   │   ├── components/        # Component-specific styles
│   │   ├── pages/             # Page-specific styles
│   │   └── app.scss           # Main stylesheet
│   └── lang/                  # Localization files
├── routes/                     # Route definitions
│   ├── web.php                # Web routes
│   ├── api.php                # API routes
│   └── console.php            # Console routes
├── storage/                    # Storage directory
│   ├── app/                   # Application storage
│   ├── framework/             # Framework storage
│   └── logs/                  # Application logs
├── tests/                      # Test files
│   ├── Feature/               # Feature tests
│   ├── Unit/                  # Unit tests
│   └── TestCase.php           # Base test case
├── vendor/                     # Composer dependencies
├── legacy/                     # Legacy code backup
│   └── index.php              # Original index.php backup
├── docs/                       # Documentation
│   ├── api/                   # API documentation
│   ├── deployment/            # Deployment guides
│   └── user-guide/            # User documentation
├── .env                        # Environment configuration
├── .env.example               # Environment template
├── artisan                    # Laravel Artisan CLI
├── composer.json              # PHP dependencies
├── package.json               # Node.js dependencies
├── webpack.mix.js             # Asset compilation configuration
└── README.md                  # Project documentation
```

## Detailed Structure Explanation

### Application Core (`app/`)
- **Console/Commands/**: Custom Artisan commands for pin management, data migration, and maintenance tasks
- **Http/Controllers/**: Organized by functionality with separate API and web controllers
- **Models/**: Eloquent models representing database entities with relationships and business logic
- **Services/**: Business logic layer separating concerns from controllers
- **Jobs/**: Background job classes for queue processing and scheduled tasks

### Configuration (`config/`)
- **pinboard.php**: Custom configuration for pin quotas, email settings, and business rules
- **database.php**: WordPress database integration configuration
- **mail.php**: SMTP configuration for email reporting system

### Database (`database/`)
- **migrations/**: Database schema changes and table creation scripts
- **seeders/**: Sample data and initial configuration seeding
- **factories/**: Test data generation for automated testing

### Frontend Assets (`resources/`)
- **js/components/**: Modular Vue.js components organized by functionality
- **css/**: SCSS files with component-based styling architecture
- **views/**: Blade templates for server-side rendering and email templates

### Routes (`routes/`)
- **api.php**: RESTful API routes for Chrome extension and AJAX requests
- **web.php**: Web interface routes for dashboard and authentication

### Testing (`tests/`)
- **Feature/**: End-to-end testing of complete workflows
- **Unit/**: Individual component and service testing

## File Naming Conventions

### PHP Files:
- **Controllers**: PascalCase with "Controller" suffix (e.g., `PinManagementController.php`)
- **Models**: PascalCase singular nouns (e.g., `PinHistory.php`, `PendingPin.php`)
- **Services**: PascalCase with "Service" suffix (e.g., `QuotaService.php`)
- **Jobs**: PascalCase with "Job" suffix (e.g., `ProcessPinJob.php`)

### JavaScript Files:
- **Components**: PascalCase (e.g., `PinDashboard.vue`, `QuotaManager.vue`)
- **Services**: camelCase with descriptive names (e.g., `pinService.js`, `apiClient.js`)
- **Pages**: PascalCase (e.g., `Dashboard.vue`, `PinHistory.vue`)

### CSS Files:
- **Component styles**: kebab-case matching component names (e.g., `pin-dashboard.scss`)
- **Utility styles**: kebab-case descriptive names (e.g., `form-controls.scss`)

## Module Organization

### Pin Management Module:
```
app/Models/Pin/
├── PinHistory.php              # Main pin history model
├── PinDetail.php               # Individual pin details
├── PendingPin.php              # Scheduled pins
└── IgnoredPost.php             # Ignored posts management

app/Services/
├── PinService.php              # Core pin operations
├── QuotaService.php            # Quota calculations
└── SchedulingService.php       # Pin scheduling logic

resources/js/components/Pin/
├── PinDashboard.vue            # Main dashboard component
├── PinHistory.vue              # Pin history display
├── QuotaDisplay.vue            # Quota information
└── PinForm.vue                 # Pin creation/editing
```

### API Module:
```
app/Http/Controllers/Api/
├── PinController.php           # Pin CRUD operations
├── ChromeExtensionController.php # Chrome extension endpoints
└── ReportController.php        # Reporting endpoints

app/Http/Resources/
├── PinResource.php             # Pin API resource
├── PostResource.php            # WordPress post resource
└── QuotaResource.php           # Quota information resource
```

### Email System Module:
```
app/Services/
└── EmailService.php            # Email generation and sending

resources/views/emails/
├── daily-report.blade.php      # Daily report template
├── pin-notification.blade.php  # Pin notification template
└── layouts/
    └── email.blade.php         # Email layout template

app/Jobs/
├── SendDailyReportJob.php      # Daily report job
└── SendNotificationJob.php     # Notification job
```

## Environment Configuration

### Development Environment:
- **Database**: Local MySQL with WordPress integration
- **Queue**: Database driver for simplicity
- **Mail**: Log driver for testing
- **Cache**: File-based caching

### Production Environment:
- **Database**: Production MySQL with connection pooling
- **Queue**: Redis for better performance
- **Mail**: SMTP with queue processing
- **Cache**: Redis for improved performance

## Build and Deployment Structure

### Asset Compilation:
- **webpack.mix.js**: Laravel Mix configuration for asset compilation
- **package.json**: Node.js dependencies for frontend build process
- **public/assets/**: Compiled and optimized assets for production

### Deployment Configuration:
- **docs/deployment/**: Deployment scripts and configuration guides
- **.env.example**: Template for environment configuration
- **composer.json**: PHP dependency management and autoloading configuration
