/**
 * Suggestions Panel Component
 * 
 * Displays today's pin suggestions with new pins and repins
 */

const SuggestionsPanel = {
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    
    setup(props) {
        const { ref, computed } = Vue;
        
        // Computed properties
        const newPins = computed(() => {
            return props.data?.new_pins || [];
        });
        
        const repins = computed(() => {
            return props.data?.repins || [];
        });
        
        const totalSuggestions = computed(() => {
            return newPins.value.length + repins.value.length;
        });
        
        const quotaInfo = computed(() => {
            return props.data?.quota_info || {};
        });
        
        // Methods
        const formatDate = (dateString) => {
            if (!dateString) return 'N/A';
            return new Date(dateString).toLocaleDateString();
        };
        
        const formatPriorityScore = (score) => {
            return typeof score === 'number' ? score.toFixed(1) : 'N/A';
        };
        
        const getCategoryBadgeClass = (category) => {
            const categoryClasses = {
                'lifestyle': 'badge-lifestyle',
                'fashion': 'badge-fashion',
                'food': 'badge-food',
                'travel': 'badge-travel',
                'home': 'badge-home',
                'diy': 'badge-diy',
                'beauty': 'badge-beauty',
                'fitness': 'badge-fitness',
                'technology': 'badge-tech',
                'business': 'badge-business'
            };
            return categoryClasses[category] || 'badge-general';
        };
        
        return {
            newPins,
            repins,
            totalSuggestions,
            quotaInfo,
            formatDate,
            formatPriorityScore,
            getCategoryBadgeClass
        };
    },
    
    template: `
        <div class="suggestions-panel">
            <!-- Summary -->
            <div class="suggestions-summary">
                <div class="summary-card">
                    <h3>{{ totalSuggestions }}</h3>
                    <p>Total Suggestions</p>
                </div>
                <div class="summary-card">
                    <h3>{{ newPins.length }}</h3>
                    <p>New Pins</p>
                </div>
                <div class="summary-card">
                    <h3>{{ repins.length }}</h3>
                    <p>Repins</p>
                </div>
                <div class="summary-card">
                    <h3>{{ quotaInfo.remaining_quota || 0 }}</h3>
                    <p>Quota Remaining</p>
                </div>
            </div>
            
            <!-- New Pins Section -->
            <section v-if="newPins.length > 0" class="pins-section">
                <h2>New Pin Suggestions</h2>
                <div class="pins-grid">
                    <div 
                        v-for="pin in newPins" 
                        :key="pin.post_id" 
                        class="pin-card new-pin"
                    >
                        <div class="pin-header">
                            <h3 class="pin-title">{{ pin.title }}</h3>
                            <div class="pin-badges">
                                <span 
                                    v-if="pin.category_info?.primary_category"
                                    :class="['badge', getCategoryBadgeClass(pin.category_info.primary_category)]"
                                >
                                    {{ pin.category_info.primary_category }}
                                </span>
                                <span class="badge badge-new">NEW</span>
                            </div>
                        </div>
                        
                        <div class="pin-meta">
                            <div class="meta-item">
                                <strong>Published:</strong> {{ formatDate(pin.publish_date) }}
                            </div>
                            <div class="meta-item">
                                <strong>Priority Score:</strong> {{ formatPriorityScore(pin.priority_score) }}
                            </div>
                            <div class="meta-item">
                                <strong>Confidence:</strong> {{ (pin.listicle_info?.confidence * 100).toFixed(0) }}%
                            </div>
                        </div>
                        
                        <div class="pin-content">
                            <p class="pin-url">
                                <a :href="pin.url" target="_blank" rel="noopener">
                                    {{ pin.url }}
                                </a>
                            </p>
                        </div>
                        
                        <div v-if="pin.suggested_boards?.length" class="suggested-boards">
                            <strong>Suggested Boards:</strong>
                            <div class="board-tags">
                                <span 
                                    v-for="board in pin.suggested_boards" 
                                    :key="board"
                                    class="board-tag"
                                >
                                    {{ board }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="pin-actions">
                            <button class="btn btn-primary btn-sm">Pin Now</button>
                            <button class="btn btn-secondary btn-sm">Schedule</button>
                            <button class="btn btn-danger btn-sm">Ignore</button>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Repins Section -->
            <section v-if="repins.length > 0" class="pins-section">
                <h2>Repin Suggestions</h2>
                <div class="pins-grid">
                    <div 
                        v-for="pin in repins" 
                        :key="pin.post_id" 
                        class="pin-card repin"
                    >
                        <div class="pin-header">
                            <h3 class="pin-title">{{ pin.title }}</h3>
                            <div class="pin-badges">
                                <span 
                                    v-if="pin.category_info?.primary_category"
                                    :class="['badge', getCategoryBadgeClass(pin.category_info.primary_category)]"
                                >
                                    {{ pin.category_info.primary_category }}
                                </span>
                                <span class="badge badge-repin">REPIN</span>
                            </div>
                        </div>
                        
                        <div class="pin-meta">
                            <div class="meta-item">
                                <strong>Published:</strong> {{ formatDate(pin.publish_date) }}
                            </div>
                            <div class="meta-item">
                                <strong>Total Pins:</strong> {{ pin.total_pins }}
                            </div>
                            <div class="meta-item">
                                <strong>Days Since Last:</strong> {{ Math.floor(pin.days_since_last_pin) }}
                            </div>
                            <div class="meta-item">
                                <strong>Priority Score:</strong> {{ formatPriorityScore(pin.priority_score) }}
                            </div>
                        </div>
                        
                        <div class="pin-content">
                            <p class="pin-url">
                                <a :href="pin.url" target="_blank" rel="noopener">
                                    {{ pin.url }}
                                </a>
                            </p>
                        </div>
                        
                        <div v-if="pin.pin_history?.pins?.length" class="pin-history">
                            <strong>Previous Pins:</strong>
                            <div class="history-list">
                                <div 
                                    v-for="(historyPin, index) in pin.pin_history.pins" 
                                    :key="index"
                                    class="history-item"
                                >
                                    <span class="history-date">{{ formatDate(historyPin.date) }}</span>
                                    <a :href="historyPin.link" target="_blank" class="history-link">
                                        View Pin
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div v-if="pin.suggested_boards?.length" class="suggested-boards">
                            <strong>Suggested Boards:</strong>
                            <div class="board-tags">
                                <span 
                                    v-for="board in pin.suggested_boards" 
                                    :key="board"
                                    class="board-tag"
                                >
                                    {{ board }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="pin-actions">
                            <button class="btn btn-primary btn-sm">Repin Now</button>
                            <button class="btn btn-secondary btn-sm">Schedule</button>
                            <button class="btn btn-danger btn-sm">Ignore</button>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Empty State -->
            <div v-if="totalSuggestions === 0" class="empty-state">
                <div class="empty-icon">📌</div>
                <h3>No Suggestions Available</h3>
                <p>There are no pin suggestions for today. This could be because:</p>
                <ul>
                    <li>Daily quota has been reached</li>
                    <li>No eligible posts found</li>
                    <li>All recent posts have been pinned</li>
                </ul>
                <button @click="$emit('refresh')" class="btn btn-primary">
                    Refresh Suggestions
                </button>
            </div>
        </div>
    `
};

// Export for global registration
window.SuggestionsPanel = SuggestionsPanel;
