<?php

/**
 * Pin Board v10 - Bootstrap Application
 * 
 * This file bootstraps the Pin Board application and integrates with WordPress
 */

// Define application constants
define('PINBOARD_BASE_PATH', dirname(__DIR__));
define('PINBOARD_APP_PATH', PINBOARD_BASE_PATH . '/app');
define('PINBOARD_CONFIG_PATH', PINBOARD_BASE_PATH . '/config');

// Simple autoloader for our classes
spl_autoload_register(function ($class) {
    // Convert namespace to file path
    $class = str_replace('\\', DIRECTORY_SEPARATOR, $class);
    
    // Check if it's our App namespace
    if (strpos($class, 'App' . DIRECTORY_SEPARATOR) === 0) {
        $file = PINBOARD_BASE_PATH . DIRECTORY_SEPARATOR . strtolower(substr($class, 4)) . '.php';
        if (file_exists($file)) {
            require_once $file;
            return true;
        }
    }
    
    return false;
});

// Load environment variables from .env file
function pinboard_load_env() {
    $env_file = PINBOARD_BASE_PATH . '/.env';
    if (file_exists($env_file)) {
        $lines = file($env_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '#') === 0) continue; // Skip comments
            if (strpos($line, '=') === false) continue; // Skip invalid lines

            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, '"\'');

            if (!empty($key)) {
                $_ENV[$key] = $value;
                putenv("$key=$value");
            }
        }
    }
}

// Load configuration
function pinboard_config($key = null, $default = null) {
    static $config = [];

    if (empty($config)) {
        // Load environment variables first
        pinboard_load_env();

        // Load configuration with environment variable support
        $config = [
            'app' => [
                'name' => $_ENV['APP_NAME'] ?? 'Pin Board v10',
                'version' => $_ENV['APP_VERSION'] ?? '10.0.0',
                'debug' => filter_var($_ENV['APP_DEBUG'] ?? true, FILTER_VALIDATE_BOOLEAN),
            ],
            'database' => [
                'host' => $_ENV['DB_HOST'] ?? (defined('DB_HOST') ? DB_HOST : 'localhost'),
                'name' => $_ENV['DB_NAME'] ?? (defined('DB_NAME') ? DB_NAME : ''),
                'user' => $_ENV['DB_USER'] ?? (defined('DB_USER') ? DB_USER : ''),
                'password' => $_ENV['DB_PASSWORD'] ?? (defined('DB_PASSWORD') ? DB_PASSWORD : ''),
                'charset' => $_ENV['DB_CHARSET'] ?? (defined('DB_CHARSET') ? DB_CHARSET : 'utf8mb4'),
            ]
        ];
    }
    
    if ($key === null) {
        return $config;
    }
    
    $keys = explode('.', $key);
    $value = $config;
    
    foreach ($keys as $k) {
        if (!isset($value[$k])) {
            return $default;
        }
        $value = $value[$k];
    }
    
    return $value;
}

// Simple database connection helper
function pinboard_db() {
    try {
        return \App\Database\Connection::getInstance()->getConnection();
    } catch (Exception $e) {
        error_log('Pin Board Database Error: ' . $e->getMessage());
        return null;
    }
}

// Simple request helper
function pinboard_request($key = null, $default = null) {
    $data = array_merge($_GET, $_POST);
    
    if ($key === null) {
        return $data;
    }
    
    return isset($data[$key]) ? $data[$key] : $default;
}

// Simple response helper
function pinboard_response($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// Error handler
function pinboard_error_handler($errno, $errstr, $errfile, $errline) {
    if (pinboard_config('app.debug')) {
        error_log("Pin Board Error: [$errno] $errstr in $errfile on line $errline");
    }
    return false;
}

// Set error handler
set_error_handler('pinboard_error_handler');

// Initialize application
function pinboard_init() {
    // Ensure WordPress is loaded
    if (!defined('ABSPATH')) {
        // Try to find and load WordPress
        $wp_paths = [
            '../../wp-load.php',
            '../wp-load.php',
            './wp-load.php'
        ];
        
        foreach ($wp_paths as $path) {
            if (file_exists($path)) {
                require_once $path;
                break;
            }
        }
    }
    
    // Set timezone
    if (function_exists('wp_timezone_string')) {
        date_default_timezone_set(wp_timezone_string());
    } else {
        date_default_timezone_set('UTC');
    }
}

// Load database connection class
require_once PINBOARD_BASE_PATH . '/app/Database/Connection.php';

// Load model classes
require_once PINBOARD_BASE_PATH . '/app/Models/BaseModel.php';
require_once PINBOARD_BASE_PATH . '/app/Models/PinHistory.php';
require_once PINBOARD_BASE_PATH . '/app/Models/PinDetail.php';
require_once PINBOARD_BASE_PATH . '/app/Models/PendingPin.php';
require_once PINBOARD_BASE_PATH . '/app/Models/IgnoredPost.php';

// Load service files
require_once PINBOARD_BASE_PATH . '/app/Services/PinService.php';
require_once PINBOARD_BASE_PATH . '/app/Services/QuotaService.php';
require_once PINBOARD_BASE_PATH . '/app/Services/PinManager.php';
require_once PINBOARD_BASE_PATH . '/app/Services/PostFilter.php';

// Load controller files
require_once PINBOARD_BASE_PATH . '/app/Controllers/BaseController.php';
require_once PINBOARD_BASE_PATH . '/app/Controllers/PinController.php';
require_once PINBOARD_BASE_PATH . '/app/Controllers/QuotaController.php';

// Initialize the application
pinboard_init();
