<?php

namespace App\Models;

use App\Database\Connection;

/**
 * Base Model Class
 * 
 * Provides Eloquent-style functionality for Pin Board models
 */
abstract class BaseModel
{
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $attributes = [];
    protected $original = [];
    protected $exists = false;
    
    protected static $connection = null;

    public function __construct(array $attributes = [])
    {
        $this->fill($attributes);
        $this->syncOriginal();
    }

    /**
     * Get database connection
     */
    protected static function getConnection()
    {
        if (static::$connection === null) {
            static::$connection = Connection::getInstance();
        }
        return static::$connection;
    }

    /**
     * Get table name
     */
    public function getTable()
    {
        if (!isset($this->table)) {
            $class = get_class($this);
            $this->table = strtolower(basename(str_replace('\\', '/', $class)));
        }
        return static::getConnection()->getTableName($this->table);
    }

    /**
     * Fill model with attributes
     */
    public function fill(array $attributes)
    {
        foreach ($attributes as $key => $value) {
            if (in_array($key, $this->fillable) || empty($this->fillable)) {
                $this->setAttribute($key, $value);
            }
        }
        return $this;
    }

    /**
     * Set attribute
     */
    public function setAttribute($key, $value)
    {
        $this->attributes[$key] = $value;
        return $this;
    }

    /**
     * Get attribute
     */
    public function getAttribute($key)
    {
        return $this->attributes[$key] ?? null;
    }

    /**
     * Magic getter
     */
    public function __get($key)
    {
        return $this->getAttribute($key);
    }

    /**
     * Magic setter
     */
    public function __set($key, $value)
    {
        $this->setAttribute($key, $value);
    }

    /**
     * Check if attribute exists
     */
    public function __isset($key)
    {
        return isset($this->attributes[$key]);
    }

    /**
     * Sync original attributes
     */
    protected function syncOriginal()
    {
        $this->original = $this->attributes;
    }

    /**
     * Check if model exists in database
     */
    public function exists()
    {
        return $this->exists;
    }

    /**
     * Save model to database
     */
    public function save()
    {
        if ($this->exists) {
            return $this->performUpdate();
        } else {
            return $this->performInsert();
        }
    }

    /**
     * Perform insert
     */
    protected function performInsert()
    {
        $attributes = $this->attributes;
        unset($attributes[$this->primaryKey]); // Remove primary key for insert
        
        $columns = array_keys($attributes);
        $values = array_values($attributes);
        $placeholders = str_repeat('?,', count($values) - 1) . '?';
        
        $sql = "INSERT INTO `{$this->getTable()}` (`" . implode('`, `', $columns) . "`) VALUES ($placeholders)";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return false;
        }
        
        $stmt->bind_param(str_repeat('s', count($values)), ...$values);
        $result = $stmt->execute();
        
        if ($result) {
            $this->setAttribute($this->primaryKey, static::getConnection()->lastInsertId());
            $this->exists = true;
            $this->syncOriginal();
        }
        
        $stmt->close();
        return $result;
    }

    /**
     * Perform update
     */
    protected function performUpdate()
    {
        $attributes = $this->attributes;
        $primaryKeyValue = $attributes[$this->primaryKey];
        unset($attributes[$this->primaryKey]);
        
        $columns = array_keys($attributes);
        $values = array_values($attributes);
        $values[] = $primaryKeyValue; // Add primary key value for WHERE clause
        
        $setClause = implode(' = ?, ', $columns) . ' = ?';
        $sql = "UPDATE `{$this->getTable()}` SET $setClause WHERE `{$this->primaryKey}` = ?";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return false;
        }
        
        $stmt->bind_param(str_repeat('s', count($values)), ...$values);
        $result = $stmt->execute();
        
        if ($result) {
            $this->syncOriginal();
        }
        
        $stmt->close();
        return $result;
    }

    /**
     * Delete model from database
     */
    public function delete()
    {
        if (!$this->exists) {
            return false;
        }
        
        $sql = "DELETE FROM `{$this->getTable()}` WHERE `{$this->primaryKey}` = ?";
        $stmt = static::getConnection()->prepare($sql);
        
        if (!$stmt) {
            return false;
        }
        
        $stmt->bind_param('s', $this->attributes[$this->primaryKey]);
        $result = $stmt->execute();
        
        if ($result) {
            $this->exists = false;
        }
        
        $stmt->close();
        return $result;
    }

    /**
     * Find model by primary key
     */
    public static function find($id)
    {
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `{$instance->primaryKey}` = ? LIMIT 1";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return null;
        }
        
        $stmt->bind_param('s', $id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $stmt->close();
            return $model;
        }
        
        $stmt->close();
        return null;
    }

    /**
     * Get all records
     */
    public static function all()
    {
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}`";
        
        $result = static::getConnection()->query($sql);
        $models = [];
        
        while ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }
        
        return $models;
    }

    /**
     * Create new model
     */
    public static function create(array $attributes)
    {
        $model = new static($attributes);
        $model->save();
        return $model;
    }

    /**
     * Convert to array
     */
    public function toArray()
    {
        return $this->attributes;
    }

    /**
     * Convert to JSON
     */
    public function toJson()
    {
        return json_encode($this->toArray());
    }

    /**
     * Simple where query
     */
    public static function where($column, $value, $operator = '=')
    {
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `{$column}` {$operator} ?";

        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }

        $stmt->bind_param('s', $value);
        $stmt->execute();
        $result = $stmt->get_result();

        $models = [];
        while ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }

        $stmt->close();
        return $models;
    }

    /**
     * First record matching where condition
     */
    public static function whereFirst($column, $value, $operator = '=')
    {
        $results = static::where($column, $value, $operator);
        return !empty($results) ? $results[0] : null;
    }
}
