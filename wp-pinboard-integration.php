<?php

/**
 * WordPress Pin Board Integration
 * 
 * This file integrates the modernized Pin Board system with WordPress
 * Include this file in your WordPress theme or as a plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Include the Pin Board bootstrap
require_once __DIR__ . '/bootstrap/app.php';

/**
 * Add Pin Board admin menu to WordPress
 */
function pinboard_add_admin_menu() {
    add_menu_page(
        'Pin Board v10',
        'Pin Board',
        'manage_options',
        'pinboard-dashboard',
        'pinboard_admin_dashboard',
        'dashicons-admin-post',
        30
    );
    
    add_submenu_page(
        'pinboard-dashboard',
        'Pin Statistics',
        'Statistics',
        'manage_options',
        'pinboard-stats',
        'pinboard_admin_stats'
    );
    
    add_submenu_page(
        'pinboard-dashboard',
        'Pin Settings',
        'Settings',
        'manage_options',
        'pinboard-settings',
        'pinboard_admin_settings'
    );
}
add_action('admin_menu', 'pinboard_add_admin_menu');

/**
 * Pin Board admin dashboard
 */
function pinboard_admin_dashboard() {
    $db = pinboard_db();
    if (!$db) {
        echo '<div class="error"><p>Database connection failed.</p></div>';
        return;
    }
    
    $today = new DateTime('now', new DateTimeZone(pinboard_config('timezone', 'Asia/Dhaka')));
    $quota_stats = pinboard_get_quota_stats($db);
    
    ?>
    <div class="wrap">
        <h1>Pin Board v10 Dashboard</h1>
        
        <div class="pinboard-dashboard">
            <div class="pinboard-stats-grid">
                <div class="pinboard-stat-card">
                    <h3>Today's Quota</h3>
                    <div class="stat-number"><?php echo $quota_stats['today']['remaining_quota']; ?></div>
                    <div class="stat-label">Remaining of <?php echo $quota_stats['today']['daily_quota']; ?></div>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: <?php echo $quota_stats['usage_percentage']; ?>%"></div>
                    </div>
                </div>
                
                <div class="pinboard-stat-card">
                    <h3>Pins Today</h3>
                    <div class="stat-number"><?php echo $quota_stats['today']['submitted_today']; ?></div>
                    <div class="stat-label">Submitted</div>
                </div>
                
                <div class="pinboard-stat-card">
                    <h3>This Week</h3>
                    <div class="stat-number"><?php echo $quota_stats['weekly']; ?></div>
                    <div class="stat-label">Total Pins</div>
                </div>
                
                <div class="pinboard-stat-card">
                    <h3>This Month</h3>
                    <div class="stat-number"><?php echo $quota_stats['monthly']; ?></div>
                    <div class="stat-label">Total Pins</div>
                </div>
            </div>
            
            <div class="pinboard-actions">
                <h3>Quick Actions</h3>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=pinboard-stats'); ?>" class="button button-primary">View Detailed Statistics</a>
                    <a href="<?php echo home_url('/pinboard-api.php?api=today_suggestions'); ?>" class="button" target="_blank">Test API Endpoint</a>
                    <a href="<?php echo admin_url('admin.php?page=pinboard-settings'); ?>" class="button">Configure Settings</a>
                </p>
            </div>
            
            <div class="pinboard-system-info">
                <h3>System Information</h3>
                <table class="widefat">
                    <tr>
                        <td><strong>Version</strong></td>
                        <td><?php echo pinboard_config('app.version'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Database Status</strong></td>
                        <td><?php echo $db ? '<span style="color: green;">Connected</span>' : '<span style="color: red;">Disconnected</span>'; ?></td>
                    </tr>
                    <tr>
                        <td><strong>Account Age</strong></td>
                        <td><?php echo $quota_stats['today']['account_age_days']; ?> days</td>
                    </tr>
                    <tr>
                        <td><strong>Timezone</strong></td>
                        <td><?php echo pinboard_config('timezone'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>API Endpoint</strong></td>
                        <td><code><?php echo home_url('/pinboard-api.php'); ?></code></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <style>
    .pinboard-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }
    .pinboard-stat-card {
        background: white;
        padding: 20px;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        text-align: center;
    }
    .pinboard-stat-card h3 {
        margin: 0 0 10px 0;
        font-size: 14px;
        color: #666;
        text-transform: uppercase;
    }
    .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #0073aa;
        margin: 10px 0;
    }
    .stat-label {
        font-size: 12px;
        color: #666;
    }
    .stat-progress {
        margin-top: 10px;
        background: #f0f0f1;
        height: 6px;
        border-radius: 3px;
        overflow: hidden;
    }
    .progress-bar {
        height: 100%;
        background: #0073aa;
        transition: width 0.3s ease;
    }
    .pinboard-actions, .pinboard-system-info {
        background: white;
        padding: 20px;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        margin: 20px 0;
    }
    .pinboard-actions h3, .pinboard-system-info h3 {
        margin-top: 0;
    }
    </style>
    <?php
}

/**
 * Pin Board statistics page
 */
function pinboard_admin_stats() {
    echo '<div class="wrap"><h1>Pin Board Statistics</h1>';
    echo '<p>Detailed statistics will be implemented in Stage 2.</p>';
    echo '</div>';
}

/**
 * Pin Board settings page
 */
function pinboard_admin_settings() {
    echo '<div class="wrap"><h1>Pin Board Settings</h1>';
    echo '<p>Settings configuration will be implemented in Stage 2.</p>';
    echo '</div>';
}

/**
 * Add API rewrite rules for clean URLs
 */
function pinboard_add_rewrite_rules() {
    add_rewrite_rule(
        '^pinboard-api/([^/]+)/?$',
        'index.php?pinboard_api=$matches[1]',
        'top'
    );
}
add_action('init', 'pinboard_add_rewrite_rules');

/**
 * Add query vars for API
 */
function pinboard_add_query_vars($vars) {
    $vars[] = 'pinboard_api';
    return $vars;
}
add_filter('query_vars', 'pinboard_add_query_vars');

/**
 * Handle API requests through WordPress
 */
function pinboard_handle_wp_api_request() {
    $api_action = get_query_var('pinboard_api');
    
    if ($api_action) {
        // Set the API parameter for our existing API handler
        $_GET['api'] = $api_action;
        
        // Include and execute the API
        require_once __DIR__ . '/routes/api.php';
        pinboard_handle_api_request();
        exit;
    }
}
add_action('template_redirect', 'pinboard_handle_wp_api_request');

/**
 * Enqueue admin styles and scripts
 */
function pinboard_admin_enqueue_scripts($hook) {
    if (strpos($hook, 'pinboard') !== false) {
        wp_enqueue_style('pinboard-admin', plugins_url('assets/admin.css', __FILE__));
        wp_enqueue_script('pinboard-admin', plugins_url('assets/admin.js', __FILE__), ['jquery']);
    }
}
add_action('admin_enqueue_scripts', 'pinboard_admin_enqueue_scripts');

/**
 * Create database tables on activation
 */
function pinboard_create_database_tables() {
    $db = pinboard_db();
    if ($db) {
        pinboard_create_tables($db);
    }
}

// Create tables when WordPress loads (if they don't exist)
add_action('wp_loaded', 'pinboard_create_database_tables');
