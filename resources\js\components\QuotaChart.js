/**
 * Quota Chart Component
 * 
 * Visual representation of quota usage and trends
 */

const QuotaChart = {
    props: {
        quotaData: {
            type: Object,
            required: true
        },
        chartType: {
            type: String,
            default: 'daily', // 'daily', 'weekly', 'monthly'
            validator: (value) => ['daily', 'weekly', 'monthly'].includes(value)
        }
    },
    
    setup(props) {
        const { ref, computed, onMounted, watch } = Vue;
        
        const chartContainer = ref(null);
        
        // Computed properties
        const chartTitle = computed(() => {
            const titles = {
                daily: 'Daily Quota Usage',
                weekly: 'Weekly Pin Activity',
                monthly: 'Monthly Pin Trends'
            };
            return titles[props.chartType];
        });
        
        const usagePercentage = computed(() => {
            const { daily_quota, submitted_today } = props.quotaData;
            return daily_quota > 0 ? Math.round((submitted_today / daily_quota) * 100) : 0;
        });
        
        const remainingPercentage = computed(() => {
            return 100 - usagePercentage.value;
        });
        
        const usageColor = computed(() => {
            const percentage = usagePercentage.value;
            if (percentage >= 90) return '#27ae60'; // Green - excellent usage
            if (percentage >= 70) return '#f39c12'; // Orange - good usage
            if (percentage >= 50) return '#f1c40f'; // Yellow - moderate usage
            return '#e74c3c'; // Red - low usage
        });
        
        const chartData = computed(() => {
            if (props.chartType === 'daily') {
                return {
                    used: props.quotaData.submitted_today || 0,
                    remaining: props.quotaData.remaining_quota || 0,
                    total: props.quotaData.daily_quota || 0
                };
            }
            // For weekly/monthly, we'd need additional data
            return {
                used: 0,
                remaining: 0,
                total: 0
            };
        });
        
        // Methods
        const drawChart = () => {
            if (!chartContainer.value) return;
            
            const container = chartContainer.value;
            const { used, remaining, total } = chartData.value;
            
            if (total === 0) {
                container.innerHTML = '<div class="no-data">No quota data available</div>';
                return;
            }
            
            // Create SVG chart
            const svgHTML = `
                <svg viewBox="0 0 200 200" class="quota-chart-svg">
                    <!-- Background circle -->
                    <circle 
                        cx="100" 
                        cy="100" 
                        r="80" 
                        fill="none" 
                        stroke="#ecf0f1" 
                        stroke-width="20"
                    />
                    
                    <!-- Usage arc -->
                    <circle 
                        cx="100" 
                        cy="100" 
                        r="80" 
                        fill="none" 
                        stroke="${usageColor.value}" 
                        stroke-width="20" 
                        stroke-dasharray="${(usagePercentage.value / 100) * 502.65} 502.65"
                        stroke-dashoffset="125.66"
                        transform="rotate(-90 100 100)"
                        class="usage-arc"
                    />
                    
                    <!-- Center text -->
                    <text x="100" y="95" text-anchor="middle" class="chart-percentage">
                        ${usagePercentage.value}%
                    </text>
                    <text x="100" y="115" text-anchor="middle" class="chart-label">
                        Used
                    </text>
                </svg>
            `;
            
            container.innerHTML = svgHTML;
        };
        
        const getStatusMessage = () => {
            const percentage = usagePercentage.value;
            if (percentage >= 90) {
                return 'Excellent quota usage! You\'re maximizing your daily potential.';
            } else if (percentage >= 70) {
                return 'Good quota usage. You\'re on track for the day.';
            } else if (percentage >= 50) {
                return 'Moderate usage. Consider pinning more content.';
            } else if (percentage > 0) {
                return 'Low quota usage. You have significant room for more pins.';
            } else {
                return 'No pins created today. Start pinning to utilize your quota.';
            }
        };
        
        const getRecommendation = () => {
            const percentage = usagePercentage.value;
            const remaining = props.quotaData.remaining_quota || 0;
            
            if (percentage >= 90) {
                return remaining > 0 ? 
                    `You have ${remaining} pins remaining. Great job!` :
                    'Daily quota reached. Excellent work today!';
            } else if (percentage >= 50) {
                return `You can still create ${remaining} more pins today.`;
            } else {
                return `Consider creating ${Math.min(remaining, 10)} more pins to better utilize your quota.`;
            }
        };
        
        // Lifecycle
        onMounted(() => {
            drawChart();
        });
        
        watch(() => props.quotaData, () => {
            drawChart();
        }, { deep: true });
        
        return {
            chartContainer,
            chartTitle,
            usagePercentage,
            remainingPercentage,
            usageColor,
            chartData,
            drawChart,
            getStatusMessage,
            getRecommendation
        };
    },
    
    template: `
        <div class="quota-chart">
            <!-- Chart Header -->
            <div class="chart-header">
                <h3>{{ chartTitle }}</h3>
                <div class="chart-summary">
                    {{ chartData.used }} / {{ chartData.total }} pins used
                </div>
            </div>
            
            <!-- Chart Visualization -->
            <div class="chart-container">
                <div ref="chartContainer" class="chart-svg-container"></div>
                
                <!-- Chart Legend -->
                <div class="chart-legend">
                    <div class="legend-item">
                        <div class="legend-color" :style="{ backgroundColor: usageColor }"></div>
                        <span class="legend-label">Used ({{ chartData.used }})</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #ecf0f1;"></div>
                        <span class="legend-label">Remaining ({{ chartData.remaining }})</span>
                    </div>
                </div>
            </div>
            
            <!-- Chart Details -->
            <div class="chart-details">
                <div class="detail-row">
                    <span class="detail-label">Usage Percentage:</span>
                    <span class="detail-value" :style="{ color: usageColor }">
                        {{ usagePercentage }}%
                    </span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Pins Remaining:</span>
                    <span class="detail-value">{{ chartData.remaining }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Daily Quota:</span>
                    <span class="detail-value">{{ chartData.total }}</span>
                </div>
            </div>
            
            <!-- Status Message -->
            <div class="chart-status">
                <div class="status-message">
                    <div class="status-icon" :style="{ color: usageColor }">
                        <span v-if="usagePercentage >= 90">🎯</span>
                        <span v-else-if="usagePercentage >= 70">👍</span>
                        <span v-else-if="usagePercentage >= 50">⚡</span>
                        <span v-else>💡</span>
                    </div>
                    <div class="status-text">
                        <p class="status-primary">{{ getStatusMessage() }}</p>
                        <p class="status-secondary">{{ getRecommendation() }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="chart-actions">
                <button class="btn btn-sm btn-outline" @click="$emit('refresh')">
                    🔄 Refresh Data
                </button>
                <button class="btn btn-sm btn-outline" @click="$emit('view-history')">
                    📊 View History
                </button>
            </div>
        </div>
    `
};

// Export for global registration
window.QuotaChart = QuotaChart;
