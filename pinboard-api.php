<?php

/**
 * Pin Board v10 API Entry Point
 * 
 * This file serves as the main entry point for the modernized Pin Board API
 * while maintaining backward compatibility with the existing Chrome extension
 */

// Include the bootstrap file
require_once __DIR__ . '/bootstrap/app.php';

// Handle API requests
if (isset($_GET['api'])) {
    require_once __DIR__ . '/routes/api.php';
    pinboard_handle_api_request();
    exit;
}

// If no API parameter, show a simple status page
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pin Board v10 - API Status</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        .status.active {
            background-color: #d4edda;
            color: #155724;
        }
        .api-endpoint {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .info-card p {
            margin: 0;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Pin Board v10 - Modernized API</h1>
        <p><span class="status active">System Active</span></p>
        
        <h2>API Status</h2>
        <p>The Pin Board v10 API is running and ready to serve requests. This modernized version maintains full backward compatibility with the existing Chrome extension while providing a structured foundation for future development.</p>
        
        <h3>Available Endpoints</h3>
        <div class="api-endpoint">
            <strong>Today's Suggestions:</strong><br>
            GET <?php echo $_SERVER['REQUEST_URI']; ?>?api=today_suggestions
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h4>Framework</h4>
                <p>Custom Laravel-inspired structure</p>
            </div>
            <div class="info-card">
                <h4>Database</h4>
                <p><?php echo pinboard_config('database.host') ? 'Connected' : 'Not Connected'; ?></p>
            </div>
            <div class="info-card">
                <h4>WordPress Integration</h4>
                <p><?php echo defined('ABSPATH') ? 'Active' : 'Not Loaded'; ?></p>
            </div>
            <div class="info-card">
                <h4>Timezone</h4>
                <p><?php echo pinboard_config('timezone', 'UTC'); ?></p>
            </div>
        </div>
        
        <h3>System Information</h3>
        <ul>
            <li><strong>Version:</strong> <?php echo pinboard_config('app.version'); ?></li>
            <li><strong>Environment:</strong> <?php echo pinboard_config('app.debug') ? 'Development' : 'Production'; ?></li>
            <li><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></li>
            <li><strong>Current Time:</strong> <?php echo current_time('Y-m-d H:i:s T'); ?></li>
        </ul>
        
        <h3>Chrome Extension Compatibility</h3>
        <p>This API maintains full compatibility with the existing Chrome extension. All existing endpoints and response formats are preserved while the underlying architecture has been modernized for better maintainability and performance.</p>
        
        <h3>Next Steps</h3>
        <ul>
            <li>Test the API endpoint with your Chrome extension</li>
            <li>Verify database connectivity and table creation</li>
            <li>Review quota calculations and pin suggestions</li>
            <li>Monitor system logs for any issues</li>
        </ul>
        
        <hr style="margin: 30px 0;">
        <p style="text-align: center; color: #6c757d; font-size: 14px;">
            Pin Board v10 - Modernized Architecture | 
            <a href="?api=today_suggestions" style="color: #007bff;">Test API</a>
        </p>
    </div>
</body>
</html>
