/**
 * Statistics Panel Component
 * 
 * Displays pin statistics and analytics
 */

const StatisticsPanel = {
    props: {
        data: {
            type: Object,
            default: () => ({})
        }
    },
    
    setup(props) {
        const { ref, computed, onMounted } = Vue;
        
        // Computed properties
        const todayStats = computed(() => {
            return props.data?.today || {};
        });
        
        const weeklyStats = computed(() => {
            return props.data?.weekly || {};
        });
        
        const monthlyStats = computed(() => {
            return props.data?.monthly || {};
        });
        
        const usagePercentage = computed(() => {
            return props.data?.usage_percentage || 0;
        });
        
        const newPinsToday = computed(() => {
            return props.data?.new_pins_today || 0;
        });
        
        // Methods
        const formatNumber = (num) => {
            return typeof num === 'number' ? num.toLocaleString() : '0';
        };
        
        const formatPercentage = (num) => {
            return typeof num === 'number' ? `${num.toFixed(1)}%` : '0%';
        };
        
        const getUsageColor = (percentage) => {
            if (percentage >= 90) return '#e74c3c'; // Red
            if (percentage >= 70) return '#f39c12'; // Orange
            if (percentage >= 50) return '#f1c40f'; // Yellow
            return '#27ae60'; // Green
        };
        
        return {
            todayStats,
            weeklyStats,
            monthlyStats,
            usagePercentage,
            newPinsToday,
            formatNumber,
            formatPercentage,
            getUsageColor
        };
    },
    
    template: `
        <div class="statistics-panel">
            <!-- Today's Statistics -->
            <section class="stats-section">
                <h2>Today's Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📌</div>
                        <div class="stat-content">
                            <h3>{{ formatNumber(todayStats.submitted_today) }}</h3>
                            <p>Pins Created Today</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🎯</div>
                        <div class="stat-content">
                            <h3>{{ formatNumber(todayStats.daily_quota) }}</h3>
                            <p>Daily Quota</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">⏳</div>
                        <div class="stat-content">
                            <h3>{{ formatNumber(todayStats.remaining_quota) }}</h3>
                            <p>Remaining Quota</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">✨</div>
                        <div class="stat-content">
                            <h3>{{ formatNumber(newPinsToday) }}</h3>
                            <p>New Pins Today</p>
                        </div>
                    </div>
                </div>
                
                <!-- Usage Progress -->
                <div class="usage-progress">
                    <h3>Daily Quota Usage</h3>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div 
                                class="progress-fill" 
                                :style="{ 
                                    width: usagePercentage + '%',
                                    backgroundColor: getUsageColor(usagePercentage)
                                }"
                            ></div>
                        </div>
                        <div class="progress-text">
                            {{ formatPercentage(usagePercentage) }} used
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Weekly Statistics -->
            <section class="stats-section">
                <h2>Weekly Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <h3>{{ formatNumber(weeklyStats.total_pins) }}</h3>
                            <p>Total Pins This Week</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">📈</div>
                        <div class="stat-content">
                            <h3>{{ formatNumber(weeklyStats.average_per_day) }}</h3>
                            <p>Average Per Day</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🔥</div>
                        <div class="stat-content">
                            <h3>{{ formatNumber(weeklyStats.best_day_count) }}</h3>
                            <p>Best Day This Week</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">📅</div>
                        <div class="stat-content">
                            <h3>{{ weeklyStats.active_days || 0 }}</h3>
                            <p>Active Days</p>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Monthly Statistics -->
            <section class="stats-section">
                <h2>Monthly Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">🗓️</div>
                        <div class="stat-content">
                            <h3>{{ formatNumber(monthlyStats.total_pins) }}</h3>
                            <p>Total Pins This Month</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">⭐</div>
                        <div class="stat-content">
                            <h3>{{ formatNumber(monthlyStats.average_per_day) }}</h3>
                            <p>Monthly Daily Average</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🎖️</div>
                        <div class="stat-content">
                            <h3>{{ formatNumber(monthlyStats.best_day_count) }}</h3>
                            <p>Best Day This Month</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">💪</div>
                        <div class="stat-content">
                            <h3>{{ monthlyStats.active_days || 0 }}</h3>
                            <p>Active Days</p>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Performance Insights -->
            <section class="stats-section">
                <h2>Performance Insights</h2>
                <div class="insights-grid">
                    <div class="insight-card">
                        <h4>Quota Efficiency</h4>
                        <div class="insight-content">
                            <div class="insight-metric">
                                {{ formatPercentage(usagePercentage) }}
                            </div>
                            <p class="insight-description">
                                <span v-if="usagePercentage >= 90" class="status-excellent">
                                    Excellent! You're maximizing your daily quota.
                                </span>
                                <span v-else-if="usagePercentage >= 70" class="status-good">
                                    Good usage of your daily quota.
                                </span>
                                <span v-else-if="usagePercentage >= 50" class="status-moderate">
                                    Moderate quota usage. Consider pinning more.
                                </span>
                                <span v-else class="status-low">
                                    Low quota usage. You have room for more pins.
                                </span>
                            </p>
                        </div>
                    </div>
                    
                    <div class="insight-card">
                        <h4>Weekly Trend</h4>
                        <div class="insight-content">
                            <div class="insight-metric">
                                {{ weeklyStats.trend || 'Stable' }}
                            </div>
                            <p class="insight-description">
                                Your pinning activity compared to last week.
                            </p>
                        </div>
                    </div>
                    
                    <div class="insight-card">
                        <h4>New Content Focus</h4>
                        <div class="insight-content">
                            <div class="insight-metric">
                                {{ formatPercentage((newPinsToday / (todayStats.submitted_today || 1)) * 100) }}
                            </div>
                            <p class="insight-description">
                                Percentage of today's pins that are new content.
                            </p>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Quick Actions -->
            <section class="stats-section">
                <h2>Quick Actions</h2>
                <div class="actions-grid">
                    <button class="action-btn">
                        <div class="action-icon">🔄</div>
                        <div class="action-text">
                            <strong>Reset Expired Pins</strong>
                            <small>Clean up old pin data</small>
                        </div>
                    </button>
                    
                    <button class="action-btn">
                        <div class="action-icon">📊</div>
                        <div class="action-text">
                            <strong>Export Statistics</strong>
                            <small>Download detailed report</small>
                        </div>
                    </button>
                    
                    <button class="action-btn">
                        <div class="action-icon">⚙️</div>
                        <div class="action-text">
                            <strong>Adjust Quota</strong>
                            <small>Modify daily limits</small>
                        </div>
                    </button>
                    
                    <button class="action-btn">
                        <div class="action-icon">🎯</div>
                        <div class="action-text">
                            <strong>Optimize Settings</strong>
                            <small>Improve pin selection</small>
                        </div>
                    </button>
                </div>
            </section>
        </div>
    `
};

// Export for global registration
window.StatisticsPanel = StatisticsPanel;
