<?php

use App\Models\PinHistory;
use App\Models\IgnoredPost;

/**
 * Post Filter Service
 * 
 * Handles post filtering, listicle detection, and categorization
 */
class PostFilter
{
    /**
     * Default listicle patterns
     */
    private static $defaultPatterns = [
        '/\b\d+\s+(ways?|tips?|tricks?|steps?|reasons?|things?|ideas?|methods?|secrets?|hacks?|facts?|benefits?|mistakes?|signs?|rules?|lessons?|strategies?|techniques?|examples?|questions?|answers?)\b/i',
        '/\b(top|best|worst|ultimate|essential|amazing|incredible|surprising|shocking|must-know|life-changing)\s+\d+/i',
        '/\blist\s+of\s+\d+/i',
        '/\d+\s+(must-have|must-see|must-try|must-read|must-know)/i',
        '/\bevery\s+\w+\s+should\s+know/i',
        '/\bhow\s+to\s+\w+\s+in\s+\d+\s+(steps?|ways?|minutes?|days?)/i'
    ];

    /**
     * Detect if a post is a listicle
     */
    public static function isListicle($post)
    {
        $title = $post->post_title ?? '';
        $content = $post->post_content ?? '';
        
        // Get patterns from config or use defaults
        $patterns = pinboard_config('filtering.listicle_patterns', self::$defaultPatterns);
        
        // Check title first (higher weight)
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $title)) {
                return [
                    'is_listicle' => true,
                    'matched_pattern' => $pattern,
                    'matched_in' => 'title',
                    'confidence' => 0.9
                ];
            }
        }
        
        // Check content (lower weight)
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return [
                    'is_listicle' => true,
                    'matched_pattern' => $pattern,
                    'matched_in' => 'content',
                    'confidence' => 0.7
                ];
            }
        }
        
        return [
            'is_listicle' => false,
            'confidence' => 0.0
        ];
    }

    /**
     * Categorize post based on content analysis
     */
    public static function categorizePost($post)
    {
        $title = strtolower($post->post_title ?? '');
        $content = strtolower($post->post_content ?? '');
        $categories = [];
        
        // Define category keywords
        $categoryKeywords = [
            'lifestyle' => ['lifestyle', 'life', 'living', 'daily', 'routine', 'habits', 'wellness', 'health'],
            'fashion' => ['fashion', 'style', 'outfit', 'clothing', 'dress', 'shoes', 'accessories', 'trend'],
            'food' => ['food', 'recipe', 'cooking', 'kitchen', 'meal', 'dinner', 'lunch', 'breakfast', 'dessert'],
            'travel' => ['travel', 'trip', 'vacation', 'destination', 'journey', 'adventure', 'explore'],
            'home' => ['home', 'house', 'decor', 'interior', 'design', 'furniture', 'room', 'decoration'],
            'diy' => ['diy', 'craft', 'handmade', 'tutorial', 'project', 'make', 'create', 'build'],
            'beauty' => ['beauty', 'makeup', 'skincare', 'hair', 'cosmetics', 'nail', 'spa'],
            'fitness' => ['fitness', 'workout', 'exercise', 'gym', 'training', 'muscle', 'cardio'],
            'technology' => ['tech', 'technology', 'gadget', 'app', 'software', 'digital', 'computer'],
            'business' => ['business', 'entrepreneur', 'startup', 'marketing', 'money', 'finance', 'career']
        ];
        
        foreach ($categoryKeywords as $category => $keywords) {
            $score = 0;
            foreach ($keywords as $keyword) {
                if (strpos($title, $keyword) !== false) {
                    $score += 3; // Title matches have higher weight
                }
                if (strpos($content, $keyword) !== false) {
                    $score += 1; // Content matches have lower weight
                }
            }
            
            if ($score > 0) {
                $categories[$category] = $score;
            }
        }
        
        // Sort by score and return top categories
        arsort($categories);
        
        return [
            'primary_category' => !empty($categories) ? array_keys($categories)[0] : 'general',
            'all_categories' => $categories,
            'confidence' => !empty($categories) ? max($categories) / 10 : 0.1
        ];
    }

    /**
     * Get new pin candidates
     */
    public static function getNewPinCandidates($posts, $pinHistory, $todayStr, $ignoredPosts)
    {
        $candidates = [];
        
        foreach ($posts as $post) {
            $postId = $post->ID;
            
            // Skip ignored posts
            if (in_array($postId, $ignoredPosts)) {
                continue;
            }
            
            // Skip posts that already have pin history
            if (isset($pinHistory[$postId])) {
                continue;
            }
            
            // Check if it's a listicle
            $listicleCheck = self::isListicle($post);
            if (!$listicleCheck['is_listicle']) {
                continue;
            }
            
            // Categorize the post
            $category = self::categorizePost($post);
            
            // Calculate priority score
            $priorityScore = self::calculatePriorityScore($post, $listicleCheck, $category);
            
            $candidates[] = [
                'post' => $post,
                'post_id' => $postId,
                'title' => $post->post_title,
                'url' => get_permalink($post->ID),
                'publish_date' => $post->post_date,
                'listicle_info' => $listicleCheck,
                'category_info' => $category,
                'priority_score' => $priorityScore,
                'suggested_boards' => self::suggestPinterestBoards($category['primary_category'])
            ];
        }
        
        return $candidates;
    }

    /**
     * Get repin candidates
     */
    public static function getRepinCandidates($posts, $pinHistory, $todayStr, $ignoredPosts)
    {
        $candidates = [];
        $repinThresholdDays = pinboard_config('repin.threshold_days', 30);
        
        foreach ($posts as $post) {
            $postId = $post->ID;
            
            // Skip ignored posts
            if (in_array($postId, $ignoredPosts)) {
                continue;
            }
            
            // Only consider posts that have pin history
            if (!isset($pinHistory[$postId])) {
                continue;
            }
            
            $history = $pinHistory[$postId];
            $pins = $history['pins'] ?? [];
            
            if (empty($pins)) {
                continue;
            }
            
            // Check if enough time has passed since last pin
            $lastPinDate = max(array_column($pins, 'date'));
            $daysSinceLastPin = (strtotime($todayStr) - strtotime($lastPinDate)) / (60 * 60 * 24);
            
            if ($daysSinceLastPin < $repinThresholdDays) {
                continue;
            }
            
            // Check if it's still a good candidate
            $listicleCheck = self::isListicle($post);
            if (!$listicleCheck['is_listicle']) {
                continue;
            }
            
            $category = self::categorizePost($post);
            $priorityScore = self::calculateRepinPriorityScore($post, $history, $daysSinceLastPin, $category);
            
            $candidates[] = [
                'post' => $post,
                'post_id' => $postId,
                'title' => $post->post_title,
                'url' => get_permalink($post->ID),
                'publish_date' => $post->post_date,
                'pin_history' => $history,
                'days_since_last_pin' => $daysSinceLastPin,
                'total_pins' => count($pins),
                'category_info' => $category,
                'priority_score' => $priorityScore,
                'suggested_boards' => self::suggestPinterestBoards($category['primary_category'])
            ];
        }
        
        return $candidates;
    }

    /**
     * Calculate priority score for new pins
     */
    private static function calculatePriorityScore($post, $listicleInfo, $categoryInfo)
    {
        $score = 0;
        
        // Base score from listicle confidence
        $score += $listicleInfo['confidence'] * 50;
        
        // Category confidence bonus
        $score += $categoryInfo['confidence'] * 30;
        
        // Post age factor (newer posts get higher score)
        $postAge = (time() - strtotime($post->post_date)) / (60 * 60 * 24);
        if ($postAge < 7) {
            $score += 20; // Very recent
        } elseif ($postAge < 30) {
            $score += 10; // Recent
        } elseif ($postAge < 90) {
            $score += 5; // Moderately recent
        }
        
        // Title quality bonus
        $titleLength = strlen($post->post_title);
        if ($titleLength >= 40 && $titleLength <= 100) {
            $score += 10; // Good title length for Pinterest
        }
        
        // Content length bonus
        $contentLength = strlen(strip_tags($post->post_content));
        if ($contentLength > 500) {
            $score += 5; // Substantial content
        }
        
        return round($score, 2);
    }

    /**
     * Calculate priority score for repins
     */
    private static function calculateRepinPriorityScore($post, $history, $daysSinceLastPin, $categoryInfo)
    {
        $score = 0;
        
        // Base score from time since last pin
        if ($daysSinceLastPin >= 90) {
            $score += 40; // Long time since last pin
        } elseif ($daysSinceLastPin >= 60) {
            $score += 30;
        } elseif ($daysSinceLastPin >= 30) {
            $score += 20;
        }
        
        // Category confidence bonus
        $score += $categoryInfo['confidence'] * 20;
        
        // Pin performance bonus (if we had analytics data)
        $totalPins = count($history['pins']);
        if ($totalPins <= 3) {
            $score += 15; // Not over-pinned
        } elseif ($totalPins <= 5) {
            $score += 10;
        }
        
        // Seasonal relevance (could be enhanced with actual seasonal data)
        $currentMonth = date('n');
        $publishMonth = date('n', strtotime($post->post_date));
        if ($currentMonth == $publishMonth) {
            $score += 10; // Same month as original publish
        }
        
        return round($score, 2);
    }

    /**
     * Suggest Pinterest boards based on category
     */
    private static function suggestPinterestBoards($category)
    {
        $boardSuggestions = [
            'lifestyle' => ['Lifestyle Tips', 'Life Hacks', 'Daily Inspiration'],
            'fashion' => ['Fashion Style', 'Outfit Ideas', 'Style Inspiration'],
            'food' => ['Recipes', 'Food Ideas', 'Cooking Tips'],
            'travel' => ['Travel Destinations', 'Travel Tips', 'Adventure'],
            'home' => ['Home Decor', 'Interior Design', 'Home Ideas'],
            'diy' => ['DIY Projects', 'Crafts', 'DIY Home'],
            'beauty' => ['Beauty Tips', 'Makeup Ideas', 'Skincare'],
            'fitness' => ['Fitness Motivation', 'Workout Ideas', 'Health Tips'],
            'technology' => ['Tech Tips', 'Gadgets', 'Digital Life'],
            'business' => ['Business Tips', 'Entrepreneur', 'Career Advice'],
            'general' => ['Life Tips', 'Inspiration', 'Ideas']
        ];
        
        return $boardSuggestions[$category] ?? $boardSuggestions['general'];
    }

    /**
     * Prioritize candidates by score
     */
    public static function prioritizeCandidates($candidates)
    {
        usort($candidates, function($a, $b) {
            return $b['priority_score'] <=> $a['priority_score'];
        });
        
        return $candidates;
    }

    /**
     * Filter candidates by minimum score threshold
     */
    public static function filterByMinScore($candidates, $minScore = 30)
    {
        return array_filter($candidates, function($candidate) use ($minScore) {
            return $candidate['priority_score'] >= $minScore;
        });
    }
}
