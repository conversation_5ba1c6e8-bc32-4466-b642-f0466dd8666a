<?php

/**
 * Base Controller
 * 
 * Common functionality for all controllers
 */
abstract class BaseController
{
    /**
     * Send JSON response
     */
    protected function jsonResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Send success response
     */
    protected function success($data = null, $message = 'Success', $statusCode = 200)
    {
        $response = [
            'success' => true,
            'message' => $message
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        return $this->jsonResponse($response, $statusCode);
    }

    /**
     * Send error response
     */
    protected function error($message = 'Error', $statusCode = 400, $errors = null)
    {
        $response = [
            'success' => false,
            'message' => $message
        ];
        
        if ($errors !== null) {
            $response['errors'] = $errors;
        }
        
        return $this->jsonResponse($response, $statusCode);
    }

    /**
     * Get request input
     */
    protected function getInput()
    {
        $input = json_decode(file_get_contents('php://input'), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return $_POST;
        }
        return $input ?: [];
    }

    /**
     * Get query parameters
     */
    protected function getQuery()
    {
        return $_GET;
    }

    /**
     * Validate required fields
     */
    protected function validateRequired($data, $requiredFields)
    {
        $missing = [];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            return [
                'valid' => false,
                'missing' => $missing,
                'message' => 'Missing required fields: ' . implode(', ', $missing)
            ];
        }
        
        return ['valid' => true];
    }

    /**
     * Sanitize input data
     */
    protected function sanitizeInput($data)
    {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Check if request method matches
     */
    protected function checkMethod($expectedMethod)
    {
        if ($_SERVER['REQUEST_METHOD'] !== strtoupper($expectedMethod)) {
            return $this->error('Method not allowed', 405);
        }
        return true;
    }

    /**
     * Get pagination parameters
     */
    protected function getPagination()
    {
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $perPage = isset($_GET['per_page']) ? max(1, min(100, intval($_GET['per_page']))) : 20;
        
        return [
            'page' => $page,
            'per_page' => $perPage
        ];
    }

    /**
     * Log controller action
     */
    protected function logAction($action, $data = null)
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'controller' => get_class($this),
            'action' => $action,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        
        if ($data) {
            $logData['data'] = $data;
        }
        
        error_log('Pin Board Controller: ' . json_encode($logData));
    }

    /**
     * Handle exceptions
     */
    protected function handleException(Exception $e, $action = 'unknown')
    {
        $this->logAction($action, [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        
        return $this->error('Internal server error', 500);
    }
}
