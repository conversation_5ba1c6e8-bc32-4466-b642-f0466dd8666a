<?php

/**
 * Test script to run quota rules migration
 */

// Include the bootstrap file
require_once __DIR__ . '/bootstrap/app.php';

// Include the migration file
require_once __DIR__ . '/database/migrations/create_quota_rules_table.php';

use App\Models\QuotaRule;

echo "<h2>Pin Board v10 - Quota Rules Migration Test</h2>\n";

try {
    echo "<h3>Running Quota Rules Migration...</h3>\n";
    
    // Run the migration
    $result = run_quota_rules_migration();
    
    if ($result) {
        echo "<p style='color: green;'>✅ Migration completed successfully!</p>\n";
        
        // Test the QuotaRule model
        echo "<h3>Testing QuotaRule Model...</h3>\n";
        
        // Get all rules
        $rules = QuotaRule::all();
        echo "<p>Total rules created: " . count($rules) . "</p>\n";
        
        // Display rules
        echo "<h4>Created Rules:</h4>\n";
        echo "<ul>\n";
        foreach ($rules as $rule) {
            echo "<li><strong>{$rule->rule_name}</strong> - {$rule->description} (Priority: {$rule->priority})</li>\n";
        }
        echo "</ul>\n";
        
        // Test quota calculation
        echo "<h3>Testing Advanced Quota Calculation...</h3>\n";
        
        $test_context = [
            'account_age_days' => 45,
            'pins_today' => 5,
            'pins_this_week' => 25,
            'pins_this_month' => 100,
            'success_rate' => 85,
            'day_of_week' => date('N'),
            'month' => date('n'),
            'hour' => date('G')
        ];
        
        $quota_result = QuotaRule::calculateQuotaFromRules($test_context);
        
        echo "<p><strong>Test Context:</strong></p>\n";
        echo "<pre>" . print_r($test_context, true) . "</pre>\n";
        
        echo "<p><strong>Quota Calculation Result:</strong></p>\n";
        echo "<pre>" . print_r($quota_result, true) . "</pre>\n";
        
        // Test advanced quota service
        echo "<h3>Testing Advanced Quota Service...</h3>\n";
        
        $db = pinboard_db();
        $advanced_quota = pinboard_calculate_advanced_quota($db);
        
        echo "<p><strong>Advanced Quota Information:</strong></p>\n";
        echo "<pre>" . print_r($advanced_quota, true) . "</pre>\n";
        
    } else {
        echo "<p style='color: red;'>❌ Migration failed!</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<hr>\n";
echo "<p><a href='dashboard.php'>← Back to Dashboard</a></p>\n";
?>
