<?php

namespace App\Models;

/**
 * PinHistory Model
 * 
 * Represents the wp_pin_history table
 * Stores the history of pinned posts
 */
class PinHistory extends BaseModel
{
    protected $table = 'pin_history';
    
    protected $fillable = [
        'post_id',
        'title',
        'url',
        'publish_date'
    ];

    /**
     * Get pin details for this history record
     */
    public function pinDetails()
    {
        return PinDetail::where('history_id', $this->id);
    }

    /**
     * Check if this post has been pinned recently
     */
    public function isPinnedRecently($days = 151)
    {
        if (!$this->publish_date) {
            return false;
        }
        
        $cutoffDate = date('Y-m-d', strtotime("-{$days} days"));
        return $this->publish_date >= $cutoffDate;
    }

    /**
     * Get posts that need to be reset (older than specified days)
     */
    public static function getExpiredPins($days = 151)
    {
        $instance = new static();
        $cutoffDate = date('Y-m-d', strtotime("-{$days} days"));
        
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `publish_date` < ?";
        $stmt = static::getConnection()->prepare($sql);
        
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('s', $cutoffDate);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $models = [];
        while ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }
        
        $stmt->close();
        return $models;
    }

    /**
     * Delete expired pins and their details
     */
    public static function deleteExpiredPins($days = 151)
    {
        $expiredPins = static::getExpiredPins($days);
        $deletedCount = 0;
        
        foreach ($expiredPins as $pin) {
            // Delete associated pin details first
            $details = $pin->pinDetails();
            foreach ($details as $detail) {
                $detail->delete();
            }
            
            // Delete the pin history record
            if ($pin->delete()) {
                $deletedCount++;
            }
        }
        
        return $deletedCount;
    }

    /**
     * Find by post ID
     */
    public static function findByPostId($postId)
    {
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` WHERE `post_id` = ? LIMIT 1";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return null;
        }
        
        $stmt->bind_param('i', $postId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $stmt->close();
            return $model;
        }
        
        $stmt->close();
        return null;
    }

    /**
     * Get all pinned post IDs
     */
    public static function getAllPinnedPostIds()
    {
        $instance = new static();
        $sql = "SELECT `post_id` FROM `{$instance->getTable()}`";
        
        $result = static::getConnection()->query($sql);
        $postIds = [];
        
        while ($row = $result->fetch_assoc()) {
            $postIds[] = (int)$row['post_id'];
        }
        
        return $postIds;
    }

    /**
     * Get pin statistics
     */
    public static function getStatistics()
    {
        $instance = new static();
        $table = $instance->getTable();
        
        // Total pins
        $totalResult = static::getConnection()->query("SELECT COUNT(*) as total FROM `{$table}`");
        $total = $totalResult->fetch_assoc()['total'];
        
        // Today's pins
        $today = date('Y-m-d');
        $todayResult = static::getConnection()->query("SELECT COUNT(*) as today FROM `{$table}` WHERE `publish_date` = '{$today}'");
        $todayCount = $todayResult->fetch_assoc()['today'];
        
        // This week's pins
        $weekStart = date('Y-m-d', strtotime('monday this week'));
        $weekResult = static::getConnection()->query("SELECT COUNT(*) as week FROM `{$table}` WHERE `publish_date` >= '{$weekStart}'");
        $weekCount = $weekResult->fetch_assoc()['week'];
        
        // This month's pins
        $monthStart = date('Y-m-01');
        $monthResult = static::getConnection()->query("SELECT COUNT(*) as month FROM `{$table}` WHERE `publish_date` >= '{$monthStart}'");
        $monthCount = $monthResult->fetch_assoc()['month'];
        
        return [
            'total' => (int)$total,
            'today' => (int)$todayCount,
            'week' => (int)$weekCount,
            'month' => (int)$monthCount
        ];
    }

    /**
     * Get recent pins
     */
    public static function getRecentPins($limit = 10)
    {
        $instance = new static();
        $sql = "SELECT * FROM `{$instance->getTable()}` ORDER BY `publish_date` DESC, `id` DESC LIMIT ?";
        
        $stmt = static::getConnection()->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('i', $limit);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $models = [];
        while ($row = $result->fetch_assoc()) {
            $model = new static($row);
            $model->exists = true;
            $model->syncOriginal();
            $models[] = $model;
        }
        
        $stmt->close();
        return $models;
    }

    /**
     * Create or update pin history
     */
    public static function createOrUpdate($postId, $title, $url, $publishDate = null)
    {
        $existing = static::findByPostId($postId);
        
        if ($existing) {
            $existing->title = $title;
            $existing->url = $url;
            if ($publishDate) {
                $existing->publish_date = $publishDate;
            }
            $existing->save();
            return $existing;
        } else {
            return static::create([
                'post_id' => $postId,
                'title' => $title,
                'url' => $url,
                'publish_date' => $publishDate ?: date('Y-m-d')
            ]);
        }
    }
}
