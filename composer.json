{"name": "pinboard/v10", "description": "Pin Board v10 - Modernized Pinterest Pin Management System", "type": "project", "license": "MIT", "require": {"php": "^7.4|^8.0", "ext-mysqli": "*", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "autoload": {"psr-4": {"App\\": "app/"}, "files": ["bootstrap/app.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "post-install-cmd": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}