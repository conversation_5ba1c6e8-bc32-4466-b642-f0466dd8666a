<?php

namespace App\Controllers;

use App\Models\QuotaRule;

/**
 * QuotaRuleController
 * 
 * Handles API endpoints for quota rule management
 */
class QuotaRuleController extends BaseController
{
    /**
     * Get all quota rules
     */
    public function index()
    {
        try {
            $rules = QuotaRule::orderBy('priority', 'asc')->get();
            
            return $this->jsonResponse([
                'success' => true,
                'data' => $rules,
                'total' => count($rules)
            ]);
        } catch (Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to fetch quota rules',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get active quota rules
     */
    public function active()
    {
        try {
            $rules = QuotaRule::getActiveRules();
            
            return $this->jsonResponse([
                'success' => true,
                'data' => $rules,
                'total' => count($rules)
            ]);
        } catch (Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to fetch active quota rules',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get rules by type
     */
    public function byType($type)
    {
        try {
            $rules = QuotaRule::getRulesByType($type);
            
            return $this->jsonResponse([
                'success' => true,
                'data' => $rules,
                'type' => $type,
                'total' => count($rules)
            ]);
        } catch (Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => "Failed to fetch rules for type: {$type}",
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new quota rule
     */
    public function create()
    {
        try {
            $data = $this->getRequestData();
            
            // Validate rule data
            $errors = QuotaRule::validateRuleData($data);
            if (!empty($errors)) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $errors
                ], 400);
            }
            
            $rule = QuotaRule::create($data);
            
            return $this->jsonResponse([
                'success' => true,
                'message' => 'Quota rule created successfully',
                'data' => $rule
            ], 201);
        } catch (Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to create quota rule',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update quota rule
     */
    public function update($id)
    {
        try {
            $rule = QuotaRule::find($id);
            if (!$rule) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => 'Quota rule not found'
                ], 404);
            }
            
            $data = $this->getRequestData();
            
            // Validate rule data
            $errors = QuotaRule::validateRuleData($data);
            if (!empty($errors)) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $errors
                ], 400);
            }
            
            $rule->update($data);
            
            return $this->jsonResponse([
                'success' => true,
                'message' => 'Quota rule updated successfully',
                'data' => $rule
            ]);
        } catch (Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to update quota rule',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete quota rule
     */
    public function delete($id)
    {
        try {
            $rule = QuotaRule::find($id);
            if (!$rule) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => 'Quota rule not found'
                ], 404);
            }
            
            $rule->delete();
            
            return $this->jsonResponse([
                'success' => true,
                'message' => 'Quota rule deleted successfully'
            ]);
        } catch (Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to delete quota rule',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle rule active status
     */
    public function toggleActive($id)
    {
        try {
            $rule = QuotaRule::find($id);
            if (!$rule) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => 'Quota rule not found'
                ], 404);
            }
            
            $rule->is_active = !$rule->is_active;
            $rule->save();
            
            return $this->jsonResponse([
                'success' => true,
                'message' => 'Quota rule status updated successfully',
                'data' => $rule
            ]);
        } catch (Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to toggle quota rule status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test rule evaluation
     */
    public function testRule($id)
    {
        try {
            $rule = QuotaRule::find($id);
            if (!$rule) {
                return $this->jsonResponse([
                    'success' => false,
                    'message' => 'Quota rule not found'
                ], 404);
            }
            
            // Get test context or use provided context
            $context = $this->getRequestData()['context'] ?? $this->getDefaultTestContext();
            
            $result = $rule->evaluateRule($context);
            
            return $this->jsonResponse([
                'success' => true,
                'rule' => $rule,
                'context' => $context,
                'evaluation_result' => $result,
                'message' => $result ? 'Rule would apply' : 'Rule would not apply'
            ]);
        } catch (Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to test quota rule',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Initialize default quota rules
     */
    public function initializeDefaults()
    {
        try {
            QuotaRule::createDefaultRules();
            
            return $this->jsonResponse([
                'success' => true,
                'message' => 'Default quota rules initialized successfully'
            ]);
        } catch (Exception $e) {
            return $this->jsonResponse([
                'success' => false,
                'message' => 'Failed to initialize default quota rules',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get rule types and operators for form building
     */
    public function metadata()
    {
        return $this->jsonResponse([
            'success' => true,
            'data' => [
                'rule_types' => [
                    QuotaRule::RULE_TYPE_AGE_BASED => 'Age Based',
                    QuotaRule::RULE_TYPE_PERFORMANCE_BASED => 'Performance Based',
                    QuotaRule::RULE_TYPE_SEASONAL => 'Seasonal',
                    QuotaRule::RULE_TYPE_CUSTOM => 'Custom'
                ],
                'operators' => [
                    QuotaRule::OPERATOR_GREATER_THAN => 'Greater Than (>)',
                    QuotaRule::OPERATOR_LESS_THAN => 'Less Than (<)',
                    QuotaRule::OPERATOR_EQUAL => 'Equal (=)',
                    QuotaRule::OPERATOR_GREATER_EQUAL => 'Greater or Equal (>=)',
                    QuotaRule::OPERATOR_LESS_EQUAL => 'Less or Equal (<=)',
                    QuotaRule::OPERATOR_BETWEEN => 'Between',
                    QuotaRule::OPERATOR_IN => 'In List'
                ],
                'condition_fields' => [
                    'account_age_days' => 'Account Age (Days)',
                    'pins_today' => 'Pins Today',
                    'pins_this_week' => 'Pins This Week',
                    'pins_this_month' => 'Pins This Month',
                    'success_rate' => 'Success Rate (%)',
                    'day_of_week' => 'Day of Week (1-7)',
                    'month' => 'Month (1-12)',
                    'hour' => 'Hour (0-23)'
                ]
            ]
        ]);
    }

    /**
     * Get default test context
     */
    private function getDefaultTestContext()
    {
        return [
            'account_age_days' => 45,
            'pins_today' => 5,
            'pins_this_week' => 25,
            'pins_this_month' => 100,
            'success_rate' => 85,
            'day_of_week' => date('N'),
            'month' => date('n'),
            'hour' => date('G')
        ];
    }
}
