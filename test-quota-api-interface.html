<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pin Board v10 - Advanced Quota API Test Interface</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .endpoint-group {
            margin-bottom: 30px;
        }
        .endpoint {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .endpoint-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .endpoint-desc {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .endpoint-url {
            background: #e9ecef;
            padding: 8px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            margin-bottom: 10px;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .response {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .setup-instructions {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>🔧 Pin Board v10 - Advanced Quota API Test Interface</h1>
    
    <div class="warning">
        <strong>⚠️ Important:</strong> This interface requires a PHP server to be running. 
        If you don't have PHP installed, please set up XAMPP, WAMP, or similar local server first.
    </div>

    <div class="setup-instructions">
        <h3>📋 Setup Instructions:</h3>
        <ol>
            <li><strong>Database Setup:</strong> Run the SQL script in <code>database/manual_quota_rules_setup.sql</code> in phpMyAdmin</li>
            <li><strong>PHP Server:</strong> Start your local PHP server (XAMPP, WAMP, etc.)</li>
            <li><strong>Test:</strong> Click the buttons below to test each API endpoint</li>
        </ol>
    </div>

    <div class="container">
        <h2>🎯 Basic Quota Endpoints</h2>
        <div class="endpoint-group">
            <div class="endpoint">
                <div class="endpoint-title">Basic Quota Information</div>
                <div class="endpoint-desc">Get current quota information (uses advanced system if enabled)</div>
                <div class="endpoint-url">GET /routes/api.php?api=quota</div>
                <button onclick="testEndpoint('quota', 'quota-response')">Test Endpoint</button>
                <div id="quota-response" class="response"></div>
            </div>

            <div class="endpoint">
                <div class="endpoint-title">Advanced Quota Information</div>
                <div class="endpoint-desc">Get advanced quota with rule breakdown</div>
                <div class="endpoint-url">GET /routes/api.php?api=quota_advanced</div>
                <button onclick="testEndpoint('quota_advanced', 'quota-advanced-response')">Test Endpoint</button>
                <div id="quota-advanced-response" class="response"></div>
            </div>

            <div class="endpoint">
                <div class="endpoint-title">Quota Statistics</div>
                <div class="endpoint-desc">Get detailed quota statistics and usage</div>
                <div class="endpoint-url">GET /routes/api.php?api=quota_stats</div>
                <button onclick="testEndpoint('quota_stats', 'quota-stats-response')">Test Endpoint</button>
                <div id="quota-stats-response" class="response"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>💡 Quota Optimization Endpoints</h2>
        <div class="endpoint-group">
            <div class="endpoint">
                <div class="endpoint-title">Quota Suggestions</div>
                <div class="endpoint-desc">Get suggestions for improving quota limits</div>
                <div class="endpoint-url">GET /routes/api.php?api=quota_suggestions</div>
                <button onclick="testEndpoint('quota_suggestions', 'suggestions-response')">Test Endpoint</button>
                <div id="suggestions-response" class="response"></div>
            </div>

            <div class="endpoint">
                <div class="endpoint-title">Optimization Tips</div>
                <div class="endpoint-desc">Get tips for better quota utilization</div>
                <div class="endpoint-url">GET /routes/api.php?api=quota_optimization_tips</div>
                <button onclick="testEndpoint('quota_optimization_tips', 'tips-response')">Test Endpoint</button>
                <div id="tips-response" class="response"></div>
            </div>

            <div class="endpoint">
                <div class="endpoint-title">Efficiency Analysis</div>
                <div class="endpoint-desc">Get quota efficiency score and analysis</div>
                <div class="endpoint-url">GET /routes/api.php?api=quota_efficiency</div>
                <button onclick="testEndpoint('quota_efficiency', 'efficiency-response')">Test Endpoint</button>
                <div id="efficiency-response" class="response"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>⚙️ Quota Rules Management</h2>
        <div class="endpoint-group">
            <div class="endpoint">
                <div class="endpoint-title">All Quota Rules</div>
                <div class="endpoint-desc">Get all quota rules (active and inactive)</div>
                <div class="endpoint-url">GET /routes/api.php?api=quota_rules</div>
                <button onclick="testEndpoint('quota_rules', 'rules-response')">Test Endpoint</button>
                <div id="rules-response" class="response"></div>
            </div>

            <div class="endpoint">
                <div class="endpoint-title">Active Quota Rules</div>
                <div class="endpoint-desc">Get only active quota rules</div>
                <div class="endpoint-url">GET /routes/api.php?api=quota_rules_active</div>
                <button onclick="testEndpoint('quota_rules_active', 'active-rules-response')">Test Endpoint</button>
                <div id="active-rules-response" class="response"></div>
            </div>

            <div class="endpoint">
                <div class="endpoint-title">Rule Metadata</div>
                <div class="endpoint-desc">Get metadata for building rule forms</div>
                <div class="endpoint-url">GET /routes/api.php?api=quota_rules_metadata</div>
                <button onclick="testEndpoint('quota_rules_metadata', 'metadata-response')">Test Endpoint</button>
                <div id="metadata-response" class="response"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🔄 Legacy Compatibility</h2>
        <div class="endpoint-group">
            <div class="endpoint">
                <div class="endpoint-title">Today's Suggestions (Chrome Extension)</div>
                <div class="endpoint-desc">Original endpoint for Chrome extension compatibility</div>
                <div class="endpoint-url">GET /routes/api.php?api=today_suggestions</div>
                <button onclick="testEndpoint('today_suggestions', 'suggestions-legacy-response')">Test Endpoint</button>
                <div id="suggestions-legacy-response" class="response"></div>
            </div>
        </div>
    </div>

    <script>
        async function testEndpoint(endpoint, responseId) {
            const responseDiv = document.getElementById(responseId);
            const button = event.target;
            
            // Show loading state
            button.disabled = true;
            button.textContent = 'Testing...';
            responseDiv.style.display = 'block';
            responseDiv.className = 'response';
            responseDiv.textContent = 'Loading...';
            
            try {
                const response = await fetch(`routes/api.php?api=${endpoint}`);
                const data = await response.json();
                
                // Format and display response
                responseDiv.textContent = JSON.stringify(data, null, 2);
                responseDiv.className = response.ok ? 'response success' : 'response error';
                
            } catch (error) {
                responseDiv.textContent = `Error: ${error.message}\n\nThis usually means:\n1. PHP server is not running\n2. Database is not set up\n3. API endpoint has an error`;
                responseDiv.className = 'response error';
            }
            
            // Reset button state
            button.disabled = false;
            button.textContent = 'Test Endpoint';
        }

        // Add some helpful information
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Pin Board v10 - Advanced Quota API Test Interface loaded');
            console.log('Make sure to:');
            console.log('1. Run the SQL script in database/manual_quota_rules_setup.sql');
            console.log('2. Start your PHP server (XAMPP, WAMP, etc.)');
            console.log('3. Test the endpoints using the buttons above');
        });
    </script>
</body>
</html>
