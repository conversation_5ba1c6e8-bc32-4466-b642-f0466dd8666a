<?php

require_once __DIR__ . '/BaseController.php';

/**
 * Quota Controller
 * 
 * Handles quota-related API endpoints
 */
class QuotaController extends BaseController
{
    /**
     * Get current quota information
     * GET /api/quota
     */
    public function index()
    {
        try {
            $this->logAction('index');
            
            $today = new DateTime('now', new DateTimeZone(pinboard_config('timezone', 'Asia/Dhaka')));
            $quotaInfo = pinboard_calculate_quota(null, $today);
            
            return $this->success($quotaInfo, 'Quota information retrieved successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'index');
        }
    }

    /**
     * Get detailed quota statistics
     * GET /api/quota/stats
     */
    public function stats()
    {
        try {
            $this->logAction('stats');
            
            $stats = pinboard_get_quota_stats();
            
            return $this->success($stats, 'Quota statistics retrieved successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'stats');
        }
    }

    /**
     * Check if pin creation is allowed
     * GET /api/quota/check?type={pin_type}
     */
    public function check()
    {
        try {
            $query = $this->getQuery();
            $this->logAction('check', $query);
            
            $pinType = isset($query['type']) ? $query['type'] : 'new';
            
            $result = pinboard_can_create_pin(null, $pinType);
            
            return $this->success($result, 'Pin creation check completed');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'check');
        }
    }

    /**
     * Get weekly pin statistics
     * GET /api/quota/weekly
     */
    public function weekly()
    {
        try {
            $this->logAction('weekly');
            
            $weeklyStats = pinboard_get_weekly_pin_stats();
            
            return $this->success($weeklyStats, 'Weekly statistics retrieved successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'weekly');
        }
    }

    /**
     * Get monthly pin statistics
     * GET /api/quota/monthly
     */
    public function monthly()
    {
        try {
            $this->logAction('monthly');
            
            $monthlyStats = pinboard_get_monthly_pin_stats();
            
            return $this->success($monthlyStats, 'Monthly statistics retrieved successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'monthly');
        }
    }

    /**
     * Get new pins count for today
     * GET /api/quota/new-pins-today
     */
    public function newPinsToday()
    {
        try {
            $this->logAction('newPinsToday');
            
            $count = pinboard_get_new_pins_count_today();
            
            return $this->success([
                'new_pins_today' => $count,
                'date' => date('Y-m-d')
            ], 'New pins count retrieved successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'newPinsToday');
        }
    }

    /**
     * Get quota configuration
     * GET /api/quota/config
     */
    public function config()
    {
        try {
            $this->logAction('config');
            
            $config = [
                'initial_quota' => pinboard_config('quota.initial', 10),
                'thresholds' => pinboard_config('quota.thresholds', [
                    30 => 20,
                    90 => 35,
                    150 => 50
                ]),
                'new_pin_percentage' => pinboard_config('quota.new_pin_percentage', 0.4),
                'reset_days_threshold' => pinboard_config('reset.days_threshold', 151),
                'timezone' => pinboard_config('timezone', 'Asia/Dhaka')
            ];
            
            return $this->success($config, 'Quota configuration retrieved successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'config');
        }
    }

    /**
     * Update quota configuration (admin only)
     * PUT /api/quota/config
     */
    public function updateConfig()
    {
        try {
            if ($this->checkMethod('PUT') !== true) return;
            
            $input = $this->sanitizeInput($this->getInput());
            $this->logAction('updateConfig', $input);
            
            // In a real implementation, you'd want to check admin permissions here
            // For now, we'll just return the current config as this is a read-only demo
            
            return $this->success(null, 'Configuration update not implemented in demo mode');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'updateConfig');
        }
    }

    /**
     * Reset expired pins manually
     * POST /api/quota/reset-expired
     */
    public function resetExpired()
    {
        try {
            if ($this->checkMethod('POST') !== true) return;
            
            $input = $this->getInput();
            $this->logAction('resetExpired', $input);
            
            $thresholdDays = isset($input['threshold_days']) ? 
                max(1, intval($input['threshold_days'])) : 
                pinboard_config('reset.days_threshold', 151);
            
            $deletedCount = pinboard_reset_expired_pins();
            
            return $this->success([
                'deleted_count' => $deletedCount,
                'threshold_days' => $thresholdDays,
                'reset_date' => date('Y-m-d H:i:s')
            ], 'Expired pins reset successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'resetExpired');
        }
    }

    /**
     * Get quota history for a specific date range
     * GET /api/quota/history?start_date={date}&end_date={date}
     */
    public function history()
    {
        try {
            $query = $this->getQuery();
            $this->logAction('history', $query);
            
            $startDate = isset($query['start_date']) ? $query['start_date'] : date('Y-m-d', strtotime('-30 days'));
            $endDate = isset($query['end_date']) ? $query['end_date'] : date('Y-m-d');
            
            // Validate dates
            if (!strtotime($startDate) || !strtotime($endDate)) {
                return $this->error('Invalid date format. Use Y-m-d format.');
            }
            
            if (strtotime($startDate) > strtotime($endDate)) {
                return $this->error('Start date cannot be after end date.');
            }
            
            // Get pin history for the date range using ORM
            $histories = PinHistory::all();
            $dailyStats = [];
            
            foreach ($histories as $history) {
                $details = PinDetail::findByHistoryId($history->id);
                foreach ($details as $detail) {
                    $pinDate = $detail->pin_date;
                    if ($pinDate >= $startDate && $pinDate <= $endDate) {
                        if (!isset($dailyStats[$pinDate])) {
                            $dailyStats[$pinDate] = 0;
                        }
                        $dailyStats[$pinDate]++;
                    }
                }
            }
            
            // Fill in missing dates with 0
            $currentDate = $startDate;
            while ($currentDate <= $endDate) {
                if (!isset($dailyStats[$currentDate])) {
                    $dailyStats[$currentDate] = 0;
                }
                $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
            }
            
            ksort($dailyStats);
            
            return $this->success([
                'start_date' => $startDate,
                'end_date' => $endDate,
                'daily_stats' => $dailyStats,
                'total_pins' => array_sum($dailyStats)
            ], 'Quota history retrieved successfully');
            
        } catch (Exception $e) {
            return $this->handleException($e, 'history');
        }
    }
}
