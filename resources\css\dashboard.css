/**
 * Pin Board Dashboard Styles
 * 
 * Modern, responsive CSS for the Vue.js dashboard
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    background-color: #f8f9fa;
}

/* Dashboard Container */
.pin-board-dashboard {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #e9ecef;
}

.dashboard-header h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    font-weight: 700;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-outline {
    background-color: transparent;
    border: 2px solid #3498db;
    color: #3498db;
}

.btn-outline:hover {
    background-color: #3498db;
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Quota Card */
.quota-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.quota-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.quota-progress {
    margin-bottom: 15px;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background-color: #27ae60;
    border-radius: 6px;
    transition: width 0.3s ease;
}

.quota-text {
    font-size: 14px;
    opacity: 0.9;
}

.quota-remaining {
    font-size: 16px;
    font-weight: 600;
}

/* Error and Loading States */
.error-message {
    background-color: #fee;
    color: #c0392b;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #e74c3c;
    margin-bottom: 20px;
}

.loading-state {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #ecf0f1;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Navigation Tabs */
.dashboard-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 30px;
    background-color: #ecf0f1;
    border-radius: 8px;
    padding: 4px;
}

.tab-button {
    flex: 1;
    padding: 12px 20px;
    background-color: transparent;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #7f8c8d;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-button:hover {
    color: #2c3e50;
    background-color: rgba(255, 255, 255, 0.5);
}

.tab-button.active {
    background-color: white;
    color: #2c3e50;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Tab Content */
.tab-content {
    min-height: 400px;
}

/* Suggestions Panel */
.suggestions-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
}

.summary-card h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #3498db;
    margin-bottom: 8px;
}

.summary-card p {
    color: #7f8c8d;
    font-size: 14px;
    font-weight: 500;
}

/* Pins Section */
.pins-section {
    margin-bottom: 40px;
}

.pins-section h2 {
    font-size: 1.8rem;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ecf0f1;
}

.pins-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

/* Pin Cards */
.pin-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.pin-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.pin-card.new-pin {
    border-left-color: #27ae60;
}

.pin-card.repin {
    border-left-color: #f39c12;
}

.pin-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.pin-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    line-height: 1.4;
    flex: 1;
    margin-right: 10px;
}

.pin-badges {
    display: flex;
    gap: 6px;
    flex-shrink: 0;
}

/* Badges */
.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-new {
    background-color: #d5f4e6;
    color: #27ae60;
}

.badge-repin {
    background-color: #fef9e7;
    color: #f39c12;
}

.badge-lifestyle {
    background-color: #e8f5e8;
    color: #27ae60;
}

.badge-fashion {
    background-color: #fce4ec;
    color: #e91e63;
}

.badge-food {
    background-color: #fff3e0;
    color: #ff9800;
}

.badge-travel {
    background-color: #e3f2fd;
    color: #2196f3;
}

.badge-home {
    background-color: #f3e5f5;
    color: #9c27b0;
}

.badge-diy {
    background-color: #e0f2f1;
    color: #009688;
}

.badge-beauty {
    background-color: #fce4ec;
    color: #e91e63;
}

.badge-fitness {
    background-color: #e8f5e8;
    color: #4caf50;
}

.badge-tech {
    background-color: #e3f2fd;
    color: #2196f3;
}

.badge-business {
    background-color: #fff3e0;
    color: #ff9800;
}

.badge-general {
    background-color: #f5f5f5;
    color: #757575;
}

/* Pin Metadata */
.pin-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.meta-item {
    font-size: 13px;
}

.meta-label {
    font-weight: 600;
    color: #7f8c8d;
}

.meta-value {
    color: #2c3e50;
    margin-left: 4px;
}

/* Pin Content */
.pin-content {
    margin-bottom: 15px;
}

.pin-url {
    margin-bottom: 10px;
}

.url-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #3498db;
    text-decoration: none;
    font-size: 14px;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.url-link:hover {
    background-color: #e9ecef;
}

.url-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Pin History */
.pin-history {
    margin-bottom: 15px;
}

.history-header {
    font-size: 13px;
    margin-bottom: 8px;
    color: #7f8c8d;
}

.history-list {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    font-size: 12px;
}

.history-date {
    color: #7f8c8d;
}

.history-link {
    color: #3498db;
    text-decoration: none;
    font-size: 11px;
}

.history-more {
    color: #7f8c8d;
    font-size: 11px;
    font-style: italic;
    text-align: center;
    margin-top: 5px;
}

/* Suggested Boards */
.suggested-boards {
    margin-bottom: 15px;
}

.boards-header {
    font-size: 13px;
    margin-bottom: 8px;
    color: #7f8c8d;
}

.board-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.board-tag {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.board-more {
    background-color: #f5f5f5;
    color: #757575;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
}

/* Pin Actions */
.pin-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
}

/* Pin Status */
.pin-status {
    border-top: 1px solid #ecf0f1;
    padding-top: 10px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-new .status-dot {
    background-color: #27ae60;
}

.status-repin .status-dot {
    background-color: #f39c12;
}

.status-text {
    color: #7f8c8d;
    font-weight: 500;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.empty-state h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.empty-state ul {
    text-align: left;
    display: inline-block;
    margin: 20px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pin-board-dashboard {
        padding: 15px;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .dashboard-header h1 {
        font-size: 2rem;
    }
    
    .suggestions-summary {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .pins-grid {
        grid-template-columns: 1fr;
    }
    
    .pin-meta {
        grid-template-columns: 1fr;
    }
    
    .pin-actions {
        flex-direction: column;
    }
    
    .dashboard-tabs {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .suggestions-summary {
        grid-template-columns: 1fr;
    }
    
    .pin-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .pin-badges {
        align-self: flex-start;
    }
}
