<?php

/**
 * Quota Service
 * 
 * Handles pin quota calculations and management
 */

/**
 * Calculate daily quota based on account age and current usage
 */
function pinboard_calculate_quota($db, $today) {
    $tables = pinboard_config('tables');
    
    // Get oldest pin date to calculate account age
    $oldest_date_query = "SELECT MIN(publish_date) as oldest_date FROM `{$tables['pin_history']}`";
    $result = $db->query($oldest_date_query);
    $row = $result->fetch_assoc();
    $oldest_date = strtotime($row['oldest_date']);
    
    $days = 0;
    if ($oldest_date) {
        $first_pin_date = new DateTime();
        $first_pin_date->setTimestamp($oldest_date);
        $interval = $first_pin_date->diff($today);
        $days = $interval->days;
    }
    
    // Calculate initial quota based on account age
    $initial_quota = pinboard_config('quota.initial', 10);
    $thresholds = pinboard_config('quota.thresholds', [
        30 => 20,
        90 => 35,
        150 => 50
    ]);
    
    foreach ($thresholds as $threshold_days => $quota) {
        if ($days >= $threshold_days) {
            $initial_quota = $quota;
        }
    }
    
    // Get number of pins submitted today
    $submitted_today = pinboard_get_today_pin_count($db);
    
    // Calculate remaining quota
    $remaining_quota = $initial_quota - $submitted_today;
    if ($remaining_quota < 0) {
        $remaining_quota = 0;
    }
    
    return [
        'daily_quota' => $initial_quota,
        'submitted_today' => $submitted_today,
        'remaining_quota' => $remaining_quota,
        'account_age_days' => $days
    ];
}

/**
 * Get count of pins submitted today
 */
function pinboard_get_today_pin_count($db) {
    $tables = pinboard_config('tables');
    $today = current_time('Y-m-d');
    
    $query = "SELECT COUNT(*) as count FROM `{$tables['pin_details']}` WHERE pin_date = '$today'";
    $result = $db->query($query);
    
    if ($result) {
        $row = $result->fetch_assoc();
        $result->free();
        return intval($row['count']);
    }
    
    return 0;
}

/**
 * Get weekly pin statistics
 */
function pinboard_get_weekly_pin_stats($db) {
    $tables = pinboard_config('tables');
    $week_ago = date('Y-m-d', strtotime('-7 days'));
    
    $query = "SELECT COUNT(*) as count FROM `{$tables['pin_details']}` WHERE pin_date >= '$week_ago'";
    $result = $db->query($query);
    
    if ($result) {
        $row = $result->fetch_assoc();
        $result->free();
        return intval($row['count']);
    }
    
    return 0;
}

/**
 * Get monthly pin statistics
 */
function pinboard_get_monthly_pin_stats($db) {
    $tables = pinboard_config('tables');
    $first_day_of_month = date('Y-m-01');
    
    $query = "SELECT COUNT(*) as count FROM `{$tables['pin_details']}` WHERE pin_date >= '$first_day_of_month'";
    $result = $db->query($query);
    
    if ($result) {
        $row = $result->fetch_assoc();
        $result->free();
        return intval($row['count']);
    }
    
    return 0;
}

/**
 * Check if user can create new pins based on quota
 */
function pinboard_can_create_pin($db, $pin_type = 'new') {
    $today = new DateTime('now', new DateTimeZone(pinboard_config('timezone', 'Asia/Dhaka')));
    $quota_info = pinboard_calculate_quota($db, $today);
    
    if ($quota_info['remaining_quota'] <= 0) {
        return [
            'allowed' => false,
            'reason' => 'Daily quota exceeded',
            'quota_info' => $quota_info
        ];
    }
    
    // For new pins, check if we have quota for new pins specifically
    if ($pin_type === 'new') {
        $new_pin_percentage = pinboard_config('quota.new_pin_percentage', 0.4);
        $max_new_pins_today = ceil($quota_info['daily_quota'] * $new_pin_percentage);
        
        // Count new pins created today (pins where this is the first pin for the post)
        $new_pins_today = pinboard_get_new_pins_count_today($db);
        
        if ($new_pins_today >= $max_new_pins_today) {
            return [
                'allowed' => false,
                'reason' => 'New pin quota exceeded for today',
                'quota_info' => $quota_info,
                'new_pins_today' => $new_pins_today,
                'max_new_pins_today' => $max_new_pins_today
            ];
        }
    }
    
    return [
        'allowed' => true,
        'quota_info' => $quota_info
    ];
}

/**
 * Get count of new pins created today
 */
function pinboard_get_new_pins_count_today($db) {
    $tables = pinboard_config('tables');
    $today = current_time('Y-m-d');
    
    // Count pins where this is the first pin for the post (created today)
    $query = "SELECT COUNT(*) as count 
              FROM `{$tables['pin_details']}` d
              INNER JOIN `{$tables['pin_history']}` h ON d.history_id = h.id
              WHERE d.pin_date = '$today'
              AND (SELECT COUNT(*) FROM `{$tables['pin_details']}` d2 WHERE d2.history_id = h.id) = 1";
    
    $result = $db->query($query);
    
    if ($result) {
        $row = $result->fetch_assoc();
        $result->free();
        return intval($row['count']);
    }
    
    return 0;
}

/**
 * Get quota usage statistics
 */
function pinboard_get_quota_stats($db) {
    $today = new DateTime('now', new DateTimeZone(pinboard_config('timezone', 'Asia/Dhaka')));
    $quota_info = pinboard_calculate_quota($db, $today);
    
    $stats = [
        'today' => $quota_info,
        'weekly' => pinboard_get_weekly_pin_stats($db),
        'monthly' => pinboard_get_monthly_pin_stats($db),
        'new_pins_today' => pinboard_get_new_pins_count_today($db),
        'usage_percentage' => 0
    ];
    
    if ($quota_info['daily_quota'] > 0) {
        $stats['usage_percentage'] = round(
            ($quota_info['submitted_today'] / $quota_info['daily_quota']) * 100, 
            1
        );
    }
    
    return $stats;
}
