<?php
/**
 * Pin Board Dashboard
 * 
 * Modern Vue.js dashboard interface for the Pin Board system
 */

// Load WordPress if available
if (file_exists('wp-config.php')) {
    require_once 'wp-config.php';
    require_once ABSPATH . 'wp-includes/wp-db.php';
}

// Load our application
require_once 'bootstrap/app.php';

// Check if user is logged in (WordPress integration)
$is_logged_in = false;
$user_name = 'Guest';

if (function_exists('is_user_logged_in') && is_user_logged_in()) {
    $is_logged_in = true;
    $current_user = wp_get_current_user();
    $user_name = $current_user->display_name;
}

// Get basic system info
$quota_info = pinboard_calculate_quota();
$system_status = [
    'database_connected' => pinboard_test_database_connection(),
    'wordpress_integrated' => function_exists('wp_get_current_user'),
    'api_available' => file_exists('routes/api.php')
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pin Board Dashboard</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📌</text></svg>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="resources/css/dashboard.css">
    
    <!-- Vue.js 3 from CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Chart.js for advanced charts (optional) -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        /* Additional inline styles for immediate loading */
        .system-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            font-size: 12px;
            z-index: 1000;
            max-width: 250px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-ok {
            background-color: #27ae60;
        }
        
        .status-error {
            background-color: #e74c3c;
        }
        
        .user-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.3s ease;
        }
        
        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-state">
            <div class="spinner"></div>
            <p>Loading Pin Board Dashboard...</p>
        </div>
    </div>
    
    <!-- System Status Panel -->
    <div class="system-status">
        <div class="user-info">
            <strong><?php echo htmlspecialchars($user_name); ?></strong>
            <?php if (!$is_logged_in): ?>
                <div style="font-size: 11px; opacity: 0.8; margin-top: 4px;">
                    <a href="wp-admin/" style="color: white;">Login to WordPress</a>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="status-item">
            <span>Database</span>
            <div class="status-indicator <?php echo $system_status['database_connected'] ? 'status-ok' : 'status-error'; ?>"></div>
        </div>
        
        <div class="status-item">
            <span>WordPress</span>
            <div class="status-indicator <?php echo $system_status['wordpress_integrated'] ? 'status-ok' : 'status-error'; ?>"></div>
        </div>
        
        <div class="status-item">
            <span>API</span>
            <div class="status-indicator <?php echo $system_status['api_available'] ? 'status-ok' : 'status-error'; ?>"></div>
        </div>
        
        <?php if ($quota_info): ?>
        <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #ecf0f1;">
            <div style="font-size: 11px; color: #7f8c8d; margin-bottom: 5px;">Quick Stats</div>
            <div style="font-size: 11px;">
                Quota: <?php echo $quota_info['submitted_today']; ?>/<?php echo $quota_info['daily_quota']; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Main Dashboard App -->
    <div id="pin-board-app">
        <!-- Vue.js will mount here -->
        <div class="pin-board-dashboard">
            <div class="loading-state">
                <div class="spinner"></div>
                <p>Initializing dashboard...</p>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script>
        // Global configuration
        window.PinBoardConfig = {
            apiBaseUrl: '/routes/api.php',
            isLoggedIn: <?php echo json_encode($is_logged_in); ?>,
            userName: <?php echo json_encode($user_name); ?>,
            systemStatus: <?php echo json_encode($system_status); ?>,
            initialQuotaInfo: <?php echo json_encode($quota_info); ?>
        };
        
        // Hide loading overlay when page is ready
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const overlay = document.getElementById('loading-overlay');
                if (overlay) {
                    overlay.classList.add('hidden');
                    setTimeout(() => overlay.remove(), 300);
                }
            }, 1000);
        });
        
        // Error handling
        window.addEventListener('error', function(e) {
            console.error('Dashboard Error:', e.error);
        });
        
        // Unhandled promise rejection handling
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled Promise Rejection:', e.reason);
        });
    </script>
    
    <!-- Load Vue Components -->
    <script src="resources/js/components/SuggestionsPanel.js"></script>
    <script src="resources/js/components/StatisticsPanel.js"></script>
    <script src="resources/js/components/PinCard.js"></script>
    <script src="resources/js/components/QuotaChart.js"></script>
    
    <!-- Load Main App -->
    <script src="resources/js/app.js"></script>
    
    <script>
        // Additional dashboard initialization
        document.addEventListener('DOMContentLoaded', function() {
            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + R for refresh
                if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                    e.preventDefault();
                    if (window.PinBoardApp && window.PinBoardApp.refreshData) {
                        window.PinBoardApp.refreshData();
                    }
                }
                
                // Escape to close modals (future feature)
                if (e.key === 'Escape') {
                    // Close any open modals
                }
            });
            
            // Auto-refresh every 10 minutes
            setInterval(function() {
                if (window.PinBoardApp && window.PinBoardApp.refreshData) {
                    console.log('Auto-refreshing dashboard data...');
                    window.PinBoardApp.refreshData();
                }
            }, 10 * 60 * 1000);
            
            // Visibility change handling (pause/resume auto-refresh)
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    console.log('Dashboard hidden - pausing auto-refresh');
                } else {
                    console.log('Dashboard visible - resuming auto-refresh');
                    if (window.PinBoardApp && window.PinBoardApp.refreshData) {
                        window.PinBoardApp.refreshData();
                    }
                }
            });
        });
    </script>
</body>
</html>
