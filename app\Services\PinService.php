<?php

use App\Models\PinHistory;
use App\Models\PinDetail;
use App\Models\PendingPin;
use App\Models\IgnoredPost;

/**
 * Pin Service
 *
 * Core business logic for pin management using ORM models
 */

/**
 * Get WordPress posts for pin processing
 */
function pinboard_get_posts() {
    if (!function_exists('get_posts')) {
        return [];
    }
    
    $args = [
        'post_status' => pinboard_config('wordpress.post_status', ['publish']),
        'posts_per_page' => -1,
        'orderby' => 'date',
        'order' => 'DESC',
        'cache_results' => true,
        'post_type' => pinboard_config('wordpress.post_types', ['post'])
    ];
    
    return get_posts($args);
}

/**
 * Load pin history from database using ORM
 */
function pinboard_load_pin_history($db = null) {
    // Reset expired pins first
    pinboard_reset_expired_pins();

    $pin_history = [];
    $histories = PinHistory::all();

    foreach ($histories as $history) {
        $post_id = $history->post_id;
        $pin_history[$post_id] = [
            'title' => $history->title,
            'url' => $history->url,
            'publish_date' => $history->publish_date,
            'pins' => []
        ];

        // Get pin details for this history
        $details = PinDetail::findByHistoryId($history->id);
        foreach ($details as $detail) {
            $pin_history[$post_id]['pins'][] = [
                'link' => $detail->pin_link,
                'date' => $detail->pin_date
            ];
        }
    }

    return $pin_history;
}

/**
 * Load ignored posts from database using ORM
 */
function pinboard_load_ignored_posts($db = null) {
    return IgnoredPost::getAllIgnoredPostIds();
}

/**
 * Reset pins that are older than the threshold using ORM
 */
function pinboard_reset_expired_pins($db = null) {
    $threshold_days = pinboard_config('reset.days_threshold', 151);

    try {
        $deletedCount = PinHistory::deleteExpiredPins($threshold_days);
        if ($deletedCount > 0) {
            error_log("Pin Board: Reset {$deletedCount} expired pins older than {$threshold_days} days");
        }
        return $deletedCount;
    } catch (Exception $e) {
        error_log("Error resetting expired pins: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get new pin candidates using PostFilter service
 */
function pinboard_get_new_pin_candidates($posts, $pin_history, $today_str, $ignored_posts) {
    return PostFilter::getNewPinCandidates($posts, $pin_history, $today_str, $ignored_posts);
}

/**
 * Prioritize new pin candidates using PostFilter service
 */
function pinboard_prioritize_new_pin_candidates($candidates) {
    return PostFilter::prioritizeCandidates($candidates);
}

/**
 * Get repin candidates using PostFilter service
 */
function pinboard_get_repin_candidates($posts, $pin_history, $today_str, $ignored_posts) {
    return PostFilter::getRepinCandidates($posts, $pin_history, $today_str, $ignored_posts);
}
