# Pin Board v10 Environment Configuration
# Copy this file to .env and update the values

# Application Configuration
APP_NAME="Pin Board v10"
APP_VERSION="10.0.0"
APP_DEBUG=true
APP_TIMEZONE="Asia/Dhaka"

# Database Configuration (WordPress Database)
DB_HOST=localhost
DB_NAME=your_wordpress_database
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_CHARSET=utf8mb4

# Pin Quota Configuration
QUOTA_INITIAL=10
QUOTA_NEW_PIN_PERCENTAGE=0.4
RESET_DAYS_THRESHOLD=151

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls
MAIL_FROM_EMAIL=<EMAIL>
MAIL_FROM_NAME="Pin Board System"

# Email Recipients (comma separated)
MAIL_RECIPIENTS="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"

# API Configuration
API_RATE_LIMIT=100

# Logging Configuration
LOG_ENABLED=true
LOG_LEVEL=info
LOG_FILE=pinboard.log

# WordPress Integration
WP_REQUIRED_CAPABILITY=publish_posts
WP_POST_TYPES=post
WP_POST_STATUS=publish
