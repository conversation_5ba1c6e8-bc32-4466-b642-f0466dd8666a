/**
 * Pin Card Component
 * 
 * Reusable component for displaying individual pin information
 */

const PinCard = {
    props: {
        pin: {
            type: Object,
            required: true
        },
        type: {
            type: String,
            default: 'new', // 'new' or 'repin'
            validator: (value) => ['new', 'repin'].includes(value)
        }
    },
    
    emits: ['pin-now', 'schedule', 'ignore'],
    
    setup(props, { emit }) {
        const { computed } = Vue;
        
        // Computed properties
        const cardClass = computed(() => {
            return `pin-card ${props.type}-pin`;
        });
        
        const badgeClass = computed(() => {
            return props.type === 'new' ? 'badge-new' : 'badge-repin';
        });
        
        const badgeText = computed(() => {
            return props.type === 'new' ? 'NEW' : 'REPIN';
        });
        
        const categoryBadgeClass = computed(() => {
            const category = props.pin.category_info?.primary_category;
            const categoryClasses = {
                'lifestyle': 'badge-lifestyle',
                'fashion': 'badge-fashion',
                'food': 'badge-food',
                'travel': 'badge-travel',
                'home': 'badge-home',
                'diy': 'badge-diy',
                'beauty': 'badge-beauty',
                'fitness': 'badge-fitness',
                'technology': 'badge-tech',
                'business': 'badge-business'
            };
            return categoryClasses[category] || 'badge-general';
        });
        
        const priorityScoreFormatted = computed(() => {
            const score = props.pin.priority_score;
            return typeof score === 'number' ? score.toFixed(1) : 'N/A';
        });
        
        const confidencePercentage = computed(() => {
            const confidence = props.pin.listicle_info?.confidence;
            return typeof confidence === 'number' ? (confidence * 100).toFixed(0) : '0';
        });
        
        // Methods
        const formatDate = (dateString) => {
            if (!dateString) return 'N/A';
            return new Date(dateString).toLocaleDateString();
        };
        
        const handlePinNow = () => {
            emit('pin-now', props.pin);
        };
        
        const handleSchedule = () => {
            emit('schedule', props.pin);
        };
        
        const handleIgnore = () => {
            emit('ignore', props.pin);
        };
        
        const truncateTitle = (title, maxLength = 80) => {
            if (!title || title.length <= maxLength) return title;
            return title.substring(0, maxLength) + '...';
        };
        
        return {
            cardClass,
            badgeClass,
            badgeText,
            categoryBadgeClass,
            priorityScoreFormatted,
            confidencePercentage,
            formatDate,
            handlePinNow,
            handleSchedule,
            handleIgnore,
            truncateTitle
        };
    },
    
    template: `
        <div :class="cardClass">
            <!-- Pin Header -->
            <div class="pin-header">
                <h3 class="pin-title" :title="pin.title">
                    {{ truncateTitle(pin.title) }}
                </h3>
                <div class="pin-badges">
                    <span 
                        v-if="pin.category_info?.primary_category"
                        :class="['badge', categoryBadgeClass]"
                    >
                        {{ pin.category_info.primary_category }}
                    </span>
                    <span :class="['badge', badgeClass]">
                        {{ badgeText }}
                    </span>
                </div>
            </div>
            
            <!-- Pin Metadata -->
            <div class="pin-meta">
                <div class="meta-item">
                    <span class="meta-label">Published:</span>
                    <span class="meta-value">{{ formatDate(pin.publish_date) }}</span>
                </div>
                
                <div class="meta-item">
                    <span class="meta-label">Priority:</span>
                    <span class="meta-value">{{ priorityScoreFormatted }}</span>
                </div>
                
                <div v-if="type === 'new'" class="meta-item">
                    <span class="meta-label">Confidence:</span>
                    <span class="meta-value">{{ confidencePercentage }}%</span>
                </div>
                
                <div v-if="type === 'repin'" class="meta-item">
                    <span class="meta-label">Total Pins:</span>
                    <span class="meta-value">{{ pin.total_pins || 0 }}</span>
                </div>
                
                <div v-if="type === 'repin'" class="meta-item">
                    <span class="meta-label">Days Since Last:</span>
                    <span class="meta-value">{{ Math.floor(pin.days_since_last_pin || 0) }}</span>
                </div>
            </div>
            
            <!-- Pin Content -->
            <div class="pin-content">
                <div class="pin-url">
                    <a :href="pin.url" target="_blank" rel="noopener" class="url-link">
                        <span class="url-icon">🔗</span>
                        <span class="url-text">{{ pin.url }}</span>
                    </a>
                </div>
            </div>
            
            <!-- Pin History (for repins) -->
            <div v-if="type === 'repin' && pin.pin_history?.pins?.length" class="pin-history">
                <div class="history-header">
                    <strong>Previous Pins ({{ pin.pin_history.pins.length }}):</strong>
                </div>
                <div class="history-list">
                    <div 
                        v-for="(historyPin, index) in pin.pin_history.pins.slice(0, 3)" 
                        :key="index"
                        class="history-item"
                    >
                        <span class="history-date">{{ formatDate(historyPin.date) }}</span>
                        <a 
                            v-if="historyPin.link" 
                            :href="historyPin.link" 
                            target="_blank" 
                            class="history-link"
                        >
                            View Pin
                        </a>
                    </div>
                    <div v-if="pin.pin_history.pins.length > 3" class="history-more">
                        +{{ pin.pin_history.pins.length - 3 }} more
                    </div>
                </div>
            </div>
            
            <!-- Suggested Boards -->
            <div v-if="pin.suggested_boards?.length" class="suggested-boards">
                <div class="boards-header">
                    <strong>Suggested Boards:</strong>
                </div>
                <div class="board-tags">
                    <span 
                        v-for="board in pin.suggested_boards.slice(0, 3)" 
                        :key="board"
                        class="board-tag"
                    >
                        {{ board }}
                    </span>
                    <span v-if="pin.suggested_boards.length > 3" class="board-more">
                        +{{ pin.suggested_boards.length - 3 }}
                    </span>
                </div>
            </div>
            
            <!-- Pin Actions -->
            <div class="pin-actions">
                <button 
                    @click="handlePinNow" 
                    class="btn btn-primary btn-sm"
                    :title="type === 'new' ? 'Pin this post now' : 'Repin this post now'"
                >
                    <span v-if="type === 'new'">📌 Pin Now</span>
                    <span v-else>🔄 Repin Now</span>
                </button>
                
                <button 
                    @click="handleSchedule" 
                    class="btn btn-secondary btn-sm"
                    title="Schedule this pin for later"
                >
                    ⏰ Schedule
                </button>
                
                <button 
                    @click="handleIgnore" 
                    class="btn btn-danger btn-sm"
                    title="Ignore this suggestion"
                >
                    ❌ Ignore
                </button>
            </div>
            
            <!-- Pin Status Indicator -->
            <div class="pin-status">
                <div v-if="type === 'new'" class="status-indicator status-new">
                    <span class="status-dot"></span>
                    <span class="status-text">Ready to pin</span>
                </div>
                <div v-else class="status-indicator status-repin">
                    <span class="status-dot"></span>
                    <span class="status-text">Ready to repin</span>
                </div>
            </div>
        </div>
    `
};

// Export for global registration
window.PinCard = PinCard;
